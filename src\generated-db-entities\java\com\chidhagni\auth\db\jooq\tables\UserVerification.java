/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.auth.db.jooq.tables;


import com.chidhagni.auth.db.jooq.DefaultSchema;
import com.chidhagni.auth.db.jooq.Indexes;
import com.chidhagni.auth.db.jooq.Keys;
import com.chidhagni.auth.db.jooq.tables.records.UserVerificationRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserVerification extends TableImpl<UserVerificationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>user_verification</code>
     */
    public static final UserVerification USER_VERIFICATION = new UserVerification();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<UserVerificationRecord> getRecordType() {
        return UserVerificationRecord.class;
    }

    /**
     * The column <code>user_verification.id</code>.
     */
    public final TableField<UserVerificationRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false).defaultValue(DSL.field(DSL.raw("gen_random_uuid()"), SQLDataType.UUID)), this, "");

    /**
     * The column <code>user_verification.contact_value</code>.
     */
    public final TableField<UserVerificationRecord, String> CONTACT_VALUE = createField(DSL.name("contact_value"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>user_verification.verification_token</code>.
     */
    public final TableField<UserVerificationRecord, String> VERIFICATION_TOKEN = createField(DSL.name("verification_token"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>user_verification.expires_at</code>.
     */
    public final TableField<UserVerificationRecord, LocalDateTime> EXPIRES_AT = createField(DSL.name("expires_at"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>user_verification.verified</code>.
     */
    public final TableField<UserVerificationRecord, Boolean> VERIFIED = createField(DSL.name("verified"), SQLDataType.BOOLEAN.defaultValue(DSL.field(DSL.raw("false"), SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>user_verification.created_at</code>.
     */
    public final TableField<UserVerificationRecord, LocalDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.LOCALDATETIME(6).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>user_verification.verified_at</code>.
     */
    public final TableField<UserVerificationRecord, LocalDateTime> VERIFIED_AT = createField(DSL.name("verified_at"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>user_verification.ip_address</code>.
     */
    public final TableField<UserVerificationRecord, String> IP_ADDRESS = createField(DSL.name("ip_address"), SQLDataType.VARCHAR(15), this, "");

    /**
     * The column <code>user_verification.device_details</code>.
     */
    public final TableField<UserVerificationRecord, String> DEVICE_DETAILS = createField(DSL.name("device_details"), SQLDataType.CLOB, this, "");

    /**
     * The column <code>user_verification.name</code>.
     */
    public final TableField<UserVerificationRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(100), this, "");

    /**
     * The column <code>user_verification.mobile_number</code>.
     */
    public final TableField<UserVerificationRecord, String> MOBILE_NUMBER = createField(DSL.name("mobile_number"), SQLDataType.VARCHAR(10), this, "");

    private UserVerification(Name alias, Table<UserVerificationRecord> aliased) {
        this(alias, aliased, null);
    }

    private UserVerification(Name alias, Table<UserVerificationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>user_verification</code> table reference
     */
    public UserVerification(String alias) {
        this(DSL.name(alias), USER_VERIFICATION);
    }

    /**
     * Create an aliased <code>user_verification</code> table reference
     */
    public UserVerification(Name alias) {
        this(alias, USER_VERIFICATION);
    }

    /**
     * Create a <code>user_verification</code> table reference
     */
    public UserVerification() {
        this(DSL.name("user_verification"), null);
    }

    public <O extends Record> UserVerification(Table<O> child, ForeignKey<O, UserVerificationRecord> key) {
        super(child, key, USER_VERIFICATION);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.asList(Indexes.IDX_VERIFICATION_TOKEN_UNVERIFIED);
    }

    @Override
    public UniqueKey<UserVerificationRecord> getPrimaryKey() {
        return Keys.USER_VERIFICATION_PKEY;
    }

    @Override
    public List<UniqueKey<UserVerificationRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.USER_VERIFICATION_CONTACT_VALUE_KEY, Keys.USER_VERIFICATION_VERIFICATION_TOKEN_KEY);
    }

    @Override
    public UserVerification as(String alias) {
        return new UserVerification(DSL.name(alias), this);
    }

    @Override
    public UserVerification as(Name alias) {
        return new UserVerification(alias, this);
    }

    @Override
    public UserVerification as(Table<?> alias) {
        return new UserVerification(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public UserVerification rename(String name) {
        return new UserVerification(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public UserVerification rename(Name name) {
        return new UserVerification(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public UserVerification rename(Table<?> name) {
        return new UserVerification(name.getQualifiedName(), null);
    }
}
