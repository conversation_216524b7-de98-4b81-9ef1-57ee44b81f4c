# -------------------------------
# Build Stage
# -------------------------------
  FROM node:20-alpine AS builder

  # ✅ Install dumb-init and CA certs for build
  RUN apk add --no-cache dumb-init ca-certificates && update-ca-certificates
  
  WORKDIR /app
  
  # Install dependencies
  COPY package*.json ./
  RUN npm install
  
  # Copy the full project
  COPY . .
  
  # Build the NestJS app
  RUN npm run build
  
  # Compile migrations to JavaScript for production
  RUN npx tsc src/database/migrations/*.ts --outDir dist/src/database/migrations --target ES2022 --module CommonJS --esModuleInterop true --skipLibCheck true --declaration false
  
  
  # -------------------------------
  # Production Stage
  # -------------------------------
  FROM node:20-alpine AS production
  
  # ✅ Install dumb-init and CA certs for runtime
  RUN apk add --no-cache dumb-init ca-certificates && update-ca-certificates
  
  WORKDIR /app
  
  # Create a non-root user
  RUN addgroup -g 1001 -S nodejs && adduser -S nestjs -u 1001
  
  # Install only production dependencies
  COPY package*.json ./
  RUN npm ci --only=production && npm cache clean --force
  
  # Copy built app, compiled migrations, and knexfile from builder
  COPY --from=builder /app/dist ./dist
  COPY --from=builder /app/knexfile.js ./
  
  # ✅ Copy entrypoint script and make it executable
  COPY docker-entrypoint.sh /usr/local/bin/docker-entrypoint.sh
  RUN chmod +x /usr/local/bin/docker-entrypoint.sh
  
  # Optional: Copy env files
  COPY .env* ./
  
  # Set ownership
  RUN chown -R nestjs:nodejs /app
  
  # Switch to non-root user
  USER nestjs
  
  # Expose port
  EXPOSE 3000
  
  # Healthcheck
  HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })" || exit 1
  
  # ✅ Use the entrypoint script to run migrations + start app
  ENTRYPOINT ["dumb-init", "--"]
  CMD ["/usr/local/bin/docker-entrypoint.sh"]
  