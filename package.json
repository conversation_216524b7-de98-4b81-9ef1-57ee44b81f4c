{"name": "backend-sample-authentication", "version": "1.0.0", "description": "Production-ready NestJS authentication backend with Knex and PostgreSQL", "main": "dist/main.js", "scripts": {"setup": "node -e \"process.platform === 'win32' ? require('child_process').spawn('cmd', ['/c', 'setup.bat'], {stdio: 'inherit'}) : require('child_process').spawn('bash', ['setup.sh'], {stdio: 'inherit'})\"", "start": "npm run migration:latest && nest start", "start:dev": "cross-env NODE_ENV=development nest start --watch", "start:prod": "cross-env NODE_ENV=production nest start", "build": "nest build", "test": "jest --config jest.config.js", "test:e2e": "jest --config ./test/jest-e2e.json", "test:coverage": "jest --coverage", "migration:make": "knex migrate:make", "migration:latest": "knex migrate:latest", "migration:rollback": "knex migrate:rollback", "migration:latest:dev": "knex --knexfile src/database/knexfile.ts migrate:latest", "sonar": "npx sonarqube-scanner"}, "dependencies": {"@nestjs-modules/mailer": "^2.0.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.3.0", "@nestjs/terminus": "^11.0.0", "@types/express-session": "^1.18.2", "@types/passport-google-oauth20": "^2.0.16", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "dotenv": "^16.3.1", "express-session": "^1.18.1", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "nest-winston": "^1.10.2", "nodemailer": "^7.0.3", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "pg": "^8.11.1", "reflect-metadata": "^0.1.14", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/testing": "^10.0.0", "@testcontainers/postgresql": "^11.0.3", "@types/bcrypt": "^5.0.0", "@types/express": "^4.17.21", "@types/jest": "^29.5.3", "@types/jsonwebtoken": "^9.0.2", "@types/node": "^20.4.2", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^3.0.8", "@types/supertest": "^2.0.12", "@types/uuid": "^10.0.0", "cross-env": "^7.0.3", "dotenv-cli": "^8.0.0", "jest": "^29.6.1", "supertest": "^6.3.3", "testcontainers": "^11.0.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.1.6"}}