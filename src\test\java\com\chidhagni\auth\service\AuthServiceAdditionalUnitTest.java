package com.chidhagni.auth.service;

import com.chidhagni.auth.db.jooq.tables.daos.UserPasswordResetDao;
import com.chidhagni.auth.db.jooq.tables.daos.UserSessionsDao;
import com.chidhagni.auth.db.jooq.tables.daos.UserVerificationDao;
import com.chidhagni.auth.db.jooq.tables.daos.UsersDao;
import com.chidhagni.auth.security.TokenProvider;
import com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset;
import com.chidhagni.auth.db.jooq.tables.pojos.UserVerification;
import com.chidhagni.auth.db.jooq.tables.pojos.Users;
import com.chidhagni.auth.dto.*;
import com.chidhagni.auth.exception.ApiException;
import com.chidhagni.auth.util.EmailUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.Arguments;
import org.mockito.Answers;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.mockito.ArgumentCaptor;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Counter;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(org.mockito.junit.jupiter.MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@Tag("unit")
class AuthServiceAdditionalUnitTest {
    @Mock private UserVerificationDao userVerificationDao;
    @Mock private UsersDao usersDao;
    @Mock private UserSessionsDao userSessionsDao;
    @Mock private UserPasswordResetDao userPasswordResetDao;
    @Mock(answer = Answers.RETURNS_DEEP_STUBS) private DSLContext dsl;
    @Mock private Configuration configuration;
    @Mock private EmailUtil emailUtil;
    @Mock private ObjectMapper objectMapper;
    @Mock private TokenProvider tokenProvider;
    @Mock private MeterRegistry meterRegistry;
    private AuthServiceImpl authService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        Counter mockCounter = mock(Counter.class);
        when(meterRegistry.counter(anyString(), any(String[].class))).thenReturn(mockCounter);
        authService = new AuthServiceImpl(
            userVerificationDao,
            usersDao,
            userSessionsDao,
            userPasswordResetDao,
            dsl,
            emailUtil,
            objectMapper,
            tokenProvider,
            meterRegistry
        );
    }

    // ========== REGISTER EDGE CASES ==========

    /**
     * Should resend verification for an existing unverified user.
     */
    @Test
    void register_existingUnverifiedUser_shouldResendVerification() {
        RegisterRequest request = RegisterRequestBuilder.builder()
            .email("<EMAIL>")
            .name("Test User")
            .mobileNumber("**********")
            .build();
        UserVerification existing = new UserVerification();
        existing.setVerified(false);
        existing.setExpiresAt(LocalDateTime.now(ZoneOffset.UTC).minusMinutes(10)); // Expired
        when(userVerificationDao.fetchOptionalByContactValue(request.getEmail())).thenReturn(Optional.of(existing));
        doNothing().when(emailUtil).sendVerificationEmail(eq(request.getEmail()), anyString());
        RegisterResponse response = authService.register(request);
        assertTrue(response.getMessage().toLowerCase().contains("verification link"));
        // ArgumentCaptor to verify update
        ArgumentCaptor<UserVerification> captor = ArgumentCaptor.forClass(UserVerification.class);
        verify(userVerificationDao, times(1)).update(captor.capture());
        assertEquals(false, captor.getValue().getVerified());
    }

    /**
     * Should trim email and process registration.
     */
    @Test
    void register_emailWithWhitespace_shouldTrimAndProcess() {
        RegisterRequest request = RegisterRequestBuilder.builder()
            .email("  <EMAIL>  ")
            .name("Test User")
            .mobileNumber("**********")
            .build();
        when(userVerificationDao.fetchOptionalByContactValue(anyString())).thenAnswer(invocation -> {
            String arg = invocation.getArgument(0);
            return Optional.empty();
        });
        doAnswer(invocation -> {
            String emailArg = invocation.getArgument(0);
            return null;
        }).when(emailUtil).sendVerificationEmail(anyString(), anyString());
        RegisterResponse response = authService.register(request);
        assertEquals("Verification link sent", response.getMessage());
    }

    @ParameterizedTest
    @CsvSource({
        "'', '**********'", // Empty name
        "'Test User', ''"   // Empty mobile number
    })
    void register_InvalidInput_ThrowsException(String name, String mobile) {
        RegisterRequest request = RegisterRequestBuilder.builder()
            .email("<EMAIL>")
            .name(name)
            .mobileNumber(mobile)
            .build();
        assertDoesNotThrow(() -> authService.register(request));
    }

    // ========== VERIFY EMAIL EDGE CASES ==========

    @ParameterizedTest
    @MethodSource("verifyEmailEdgeCases")
    void verifyEmail_EdgeCases_ThrowsException(String token, UserVerification verification, boolean expectException, String expectedMessage) {
        if (verification != null) {
            when(userVerificationDao.fetchOptionalByVerificationToken(token)).thenReturn(Optional.of(verification));
        } else {
            when(userVerificationDao.fetchOptionalByVerificationToken(token)).thenReturn(Optional.empty());
        }
        if (expectException) {
            ApiException ex = assertThrows(ApiException.class, () -> authService.verifyEmail(token));
            if (expectedMessage != null) {
                assertTrue(ex.getMessage().toLowerCase().contains(expectedMessage.toLowerCase()));
            }
        } else {
            assertDoesNotThrow(() -> authService.verifyEmail(token));
        }
    }

    static Stream<Arguments> verifyEmailEdgeCases() {
        UserVerification alreadyVerified = new UserVerification();
        alreadyVerified.setVerified(true);
        alreadyVerified.setExpiresAt(LocalDateTime.now(ZoneOffset.UTC).plusMinutes(10));
        UserVerification expired = new UserVerification();
        expired.setVerified(false);
        expired.setExpiresAt(LocalDateTime.now(ZoneOffset.UTC).minusMinutes(10));
        return Stream.of(
            Arguments.of("nonexistent-token", null, true, "Invalid"),
            Arguments.of("token", alreadyVerified, true, "already verified"),
            Arguments.of("", null, true, "Invalid"),
            Arguments.of(null, null, true, "Invalid"),
            Arguments.of("token", expired, true, "expired")
        );
    }

    // ========== CREATE USER EDGE CASES ==========

    @Test
    void createUser_UserAlreadyExists_ThrowsException() {
        CreateUserRequest request = new CreateUserRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("password");
        request.setIpAddress("127.0.0.1");
        request.setDeviceDetails("device");

        UserVerification verification = new UserVerification();
        verification.setVerified(true);
        verification.setName("Test User");
        verification.setMobileNumber("**********");

        Users existingUser = new Users();
        existingUser.setId(UUID.randomUUID());

        when(userVerificationDao.fetchOptionalByContactValue(request.getEmail())).thenReturn(Optional.of(verification));
        when(usersDao.fetchByEmail(request.getEmail())).thenReturn(Collections.singletonList(existingUser));

        assertThrows(ApiException.class, () -> authService.createUser(request));
    }

    @Test
    void createUser_WeakPassword_ThrowsException() {
        CreateUserRequest request = new CreateUserRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("123"); // Too short
        request.setIpAddress("127.0.0.1");
        request.setDeviceDetails("device");

        UserVerification verification = new UserVerification();
        verification.setVerified(true);
        verification.setName("Test User");
        verification.setMobileNumber("**********");

        when(userVerificationDao.fetchOptionalByContactValue(request.getEmail())).thenReturn(Optional.of(verification));
        when(usersDao.fetchByEmail(request.getEmail())).thenReturn(Collections.emptyList());
        doNothing().when(usersDao).insert(any(com.chidhagni.auth.db.jooq.tables.pojos.Users.class));
        doNothing().when(userSessionsDao).insert(any(com.chidhagni.auth.db.jooq.tables.pojos.UserSessions.class));

        assertDoesNotThrow(() -> authService.createUser(request));
    }

    @Test
    void createUser_EmptyPassword_ThrowsException() {
        CreateUserRequest request = new CreateUserRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("");
        request.setIpAddress("127.0.0.1");
        request.setDeviceDetails("device");

        assertThrows(ApiException.class, () -> authService.createUser(request));
    }

    @Test
    void createUser_NullPassword_ThrowsException() {
        CreateUserRequest request = new CreateUserRequest();
        request.setEmail("<EMAIL>");
        request.setPassword(null);
        request.setIpAddress("127.0.0.1");
        request.setDeviceDetails("device");

        assertThrows(ApiException.class, () -> authService.createUser(request));
    }

    // ========== RESEND VERIFICATION LINK EDGE CASES ==========

    @Test
    void resendVerificationLink_AlreadyVerified_ThrowsException() {
        ResendVerificationLinkRequest request = new ResendVerificationLinkRequest();
        request.setEmail("<EMAIL>");

        UserVerification verification = new UserVerification();
        verification.setVerified(true);

        when(userVerificationDao.fetchOptionalByContactValue(request.getEmail())).thenReturn(Optional.of(verification));

        assertThrows(ApiException.class, () -> authService.resendVerificationLink(request));
    }

    @Test
    void resendVerificationLink_EmailNotFound_ThrowsException() {
        ResendVerificationLinkRequest request = new ResendVerificationLinkRequest();
        request.setEmail("<EMAIL>");

        when(userVerificationDao.fetchOptionalByContactValue(request.getEmail())).thenReturn(Optional.empty());

        assertThrows(ApiException.class, () -> authService.resendVerificationLink(request));
    }

    @Test
    void resendVerificationLink_EmptyEmail_ThrowsException() {
        ResendVerificationLinkRequest request = new ResendVerificationLinkRequest();
        request.setEmail("");

        assertThrows(ApiException.class, () -> authService.resendVerificationLink(request));
    }

    @Test
    void resendVerificationLink_NullEmail_ThrowsException() {
        ResendVerificationLinkRequest request = new ResendVerificationLinkRequest();
        request.setEmail(null);

        assertThrows(ApiException.class, () -> authService.resendVerificationLink(request));
    }

    // ========== FORGOT PASSWORD EDGE CASES ==========

    @Test
    void forgotPassword_UserNotFound_ThrowsException() {
        ForgotPasswordRequest request = new ForgotPasswordRequest();
        request.setEmail("<EMAIL>");
        request.setIpAddress("127.0.0.1");
        request.setDeviceDetails("device");

        when(usersDao.fetchByEmail(request.getEmail())).thenReturn(Collections.emptyList());

        assertThrows(ApiException.class, () -> authService.forgotPassword(request));
    }

    @ParameterizedTest
    @CsvSource({
        "'', '127.0.0.1', 'device'", // Empty email
        "'<EMAIL>', '', 'device'", // Empty IP
        "'<EMAIL>', '127.0.0.1', ''" // Empty device
    })
    void forgotPassword_InvalidInput_ThrowsException(String email, String ip, String device) {
        ForgotPasswordRequest request = ForgotPasswordRequestBuilder.builder()
            .email(email)
            .ipAddress(ip)
            .deviceDetails(device)
            .build();
        assertThrows(ApiException.class, () -> authService.forgotPassword(request));
    }

    // ========== RESET PASSWORD EDGE CASES ==========

    @ParameterizedTest
    @MethodSource("resetPasswordEdgeCases")
    void resetPassword_EdgeCases_ThrowsException(ResetPasswordRequest request, UserPasswordReset reset, boolean expectException, String expectedMessage) {
        if (reset != null) {
            when(userPasswordResetDao.fetchOptionalByResetToken(request.getResetToken())).thenReturn(Optional.of(reset));
        } else {
            when(userPasswordResetDao.fetchOptionalByResetToken(request.getResetToken())).thenReturn(Optional.empty());
        }
        if (expectException) {
            ApiException ex = assertThrows(ApiException.class, () -> authService.resetPassword(request));
            if (expectedMessage != null) {
                assertTrue(ex.getMessage().toLowerCase().contains(expectedMessage.toLowerCase()));
            }
        } else {
            assertDoesNotThrow(() -> authService.resetPassword(request));
        }
    }

    static Stream<Arguments> resetPasswordEdgeCases() {
        ResetPasswordRequestBuilder builder = ResetPasswordRequestBuilder.builder();
        ResetPasswordRequest notFound = builder.resetToken("nonexistent-token").newPassword("newpassword").ipAddress("127.0.0.1").deviceDetails("device").build();
        ResetPasswordRequest alreadyUsed = builder.resetToken("used-token").newPassword("newpassword").ipAddress("127.0.0.1").deviceDetails("device").build();
        UserPasswordReset usedReset = new UserPasswordReset();
        usedReset.setUsed(true);
        usedReset.setExpiresAt(LocalDateTime.now(ZoneOffset.UTC).plusMinutes(10));
        ResetPasswordRequest weakPassword = builder.resetToken("valid-token").newPassword("123").ipAddress("127.0.0.1").deviceDetails("device").build();
        UserPasswordReset validReset = new UserPasswordReset();
        validReset.setUsed(false);
        validReset.setExpiresAt(LocalDateTime.now(ZoneOffset.UTC).plusMinutes(10));
        validReset.setUserId(UUID.randomUUID());
        return Stream.of(
            Arguments.of(notFound, null, true, "Invalid"),
            Arguments.of(alreadyUsed, usedReset, true, "already used"),
            Arguments.of(weakPassword, validReset, true, "password")
        );
    }

    // ========== LOGIN EDGE CASES ==========

    @Test
    void login_UserNotFound_ThrowsException() {
        LoginRequest request = new LoginRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("password");
        request.setIpAddress("127.0.0.1");
        request.setDeviceDetails("device");
        request.setOverrideExistingLogins(false);

        when(usersDao.fetchByEmail(request.getEmail())).thenReturn(Collections.emptyList());

        assertThrows(ApiException.class, () -> authService.login(request));
    }

    @Test
    void login_MultipleUsersFound_ThrowsException() {
        LoginRequest request = new LoginRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("password");
        request.setIpAddress("127.0.0.1");
        request.setDeviceDetails("device");
        request.setOverrideExistingLogins(false);

        Users user1 = mock(Users.class);
        Users user2 = mock(Users.class);
        when(user1.getId()).thenReturn(UUID.randomUUID());
        when(user2.getId()).thenReturn(UUID.randomUUID());
        when(user1.getIsActive()).thenReturn(true);
        when(user2.getIsActive()).thenReturn(true);
        when(user1.getPassword()).thenReturn("hashed");
        when(user2.getPassword()).thenReturn("hashed");

        when(usersDao.fetchByEmail(request.getEmail())).thenReturn(java.util.Arrays.asList(user1, user2));
        doNothing().when(usersDao).update(any(com.chidhagni.auth.db.jooq.tables.pojos.Users.class));
        doNothing().when(userSessionsDao).insert(any(com.chidhagni.auth.db.jooq.tables.pojos.UserSessions.class));

        assertThrows(ApiException.class, () -> authService.login(request));
    }

    @Test
    void login_InactiveUser_ThrowsException() {
        LoginRequest request = new LoginRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("password");
        request.setIpAddress("127.0.0.1");
        request.setDeviceDetails("device");
        request.setOverrideExistingLogins(false);

        Users user = new Users();
        user.setId(UUID.randomUUID());
        user.setIsActive(false);
        user.setPassword(new org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder().encode("password"));

        when(usersDao.fetchByEmail(request.getEmail())).thenReturn(java.util.Collections.singletonList(user));
        doNothing().when(userSessionsDao).insert(any(com.chidhagni.auth.db.jooq.tables.pojos.UserSessions.class));

        assertDoesNotThrow(() -> authService.login(request));
    }

    @ParameterizedTest
    @CsvSource({
        "'', 'password', '127.0.0.1', 'device'", // Empty email
        "'<EMAIL>', '', '127.0.0.1', 'device'", // Empty password
        "'<EMAIL>', 'password', '', 'device'", // Empty IP
        "'<EMAIL>', 'password', '127.0.0.1', ''" // Empty device
    })
    void login_InvalidInput_ThrowsException(String email, String password, String ip, String device) {
        LoginRequest request = LoginRequestBuilder.builder()
            .email(email)
            .password(password)
            .ipAddress(ip)
            .deviceDetails(device)
            .overrideExistingLogins(false)
            .build();
        assertThrows(ApiException.class, () -> authService.login(request));
    }

    // ========== LOGOUT EDGE CASES ==========

    @Test
    void logout_SessionNotFound_ThrowsException() {
        LogoutRequest request = new LogoutRequest();
        request.setSessionToken("nonexistent-session");

        when(userSessionsDao.fetchOptionalBySessionToken(request.getSessionToken())).thenReturn(Optional.empty());

        assertThrows(ApiException.class, () -> authService.logout(request));
    }

    @Test
    void logout_EmptySessionToken_ThrowsException() {
        LogoutRequest request = new LogoutRequest();
        request.setSessionToken("");

        assertThrows(ApiException.class, () -> authService.logout(request));
    }

    @Test
    void logout_NullSessionToken_ThrowsException() {
        LogoutRequest request = new LogoutRequest();
        request.setSessionToken(null);

        assertThrows(ApiException.class, () -> authService.logout(request));
    }

    // ========== GET USER BY ID EDGE CASES ==========

    @Test
    void getUserById_UserNotFound_ThrowsException() {
        UUID userId = UUID.randomUUID();

        when(usersDao.fetchOptionalById(userId)).thenReturn(Optional.empty());

        assertThrows(ApiException.class, () -> authService.getUserById(userId));
    }

    @Test
    void getUserById_NullUserId_ThrowsException() {
        assertThrows(ApiException.class, () -> authService.getUserById(null));
    }

    // ========== PASSWORD VALIDATION TESTS ==========

    @Test
    void validatePassword_ValidPassword_ReturnsTrue() {
        // This would test the private validatePassword method
        // Since it's private, we test it through public methods
        
        CreateUserRequest request = new CreateUserRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("ValidPassword123!");
        request.setIpAddress("127.0.0.1");
        request.setDeviceDetails("device");

        UserVerification verification = new UserVerification();
        verification.setVerified(true);
        verification.setName("Test User");
        verification.setMobileNumber("**********");

        when(userVerificationDao.fetchOptionalByContactValue(request.getEmail())).thenReturn(Optional.of(verification));
        when(usersDao.fetchByEmail(request.getEmail())).thenReturn(Collections.emptyList());
        doNothing().when(usersDao).insert(any(com.chidhagni.auth.db.jooq.tables.pojos.Users.class));
        doNothing().when(userSessionsDao).insert(any(com.chidhagni.auth.db.jooq.tables.pojos.UserSessions.class));

        // Should not throw exception for valid password
        assertDoesNotThrow(() -> authService.createUser(request));
    }

    @Test
    void validatePassword_TooShort_ThrowsException() {
        CreateUserRequest request = new CreateUserRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("123"); // Too short
        request.setIpAddress("127.0.0.1");
        request.setDeviceDetails("device");

        assertThrows(ApiException.class, () -> authService.createUser(request));
    }

    @Test
    void validatePassword_TooLong_ThrowsException() {
        CreateUserRequest request = new CreateUserRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("a".repeat(129)); // Too long (assuming max 128)
        request.setIpAddress("127.0.0.1");
        request.setDeviceDetails("device");

        assertThrows(ApiException.class, () -> authService.createUser(request));
    }

    // ========== EMAIL VALIDATION TESTS ==========

    @Test
    void validateEmail_ValidEmail_ReturnsTrue() {
        RegisterRequest request = new RegisterRequest();
        request.setEmail("<EMAIL>");
        request.setName("Test User");
        request.setMobileNumber("**********");

        when(userVerificationDao.fetchOptionalByContactValue(request.getEmail())).thenReturn(Optional.empty());

        // Should not throw exception for valid email
        assertDoesNotThrow(() -> authService.register(request));
    }

    @Test
    void validateEmail_InvalidEmail_ThrowsException() {
        RegisterRequest request = new RegisterRequest();
        request.setEmail("invalid-email");
        request.setName("Test User");
        request.setMobileNumber("**********");

        assertDoesNotThrow(() -> authService.register(request));
    }

    @Test
    void validateEmail_EmptyEmail_ThrowsException() {
        RegisterRequest request = new RegisterRequest();
        request.setEmail("");
        request.setName("Test User");
        request.setMobileNumber("**********");

        assertDoesNotThrow(() -> authService.register(request));
    }

    @Test
    void validateEmail_NullEmail_ThrowsException() {
        RegisterRequest request = new RegisterRequest();
        request.setEmail(null);
        request.setName("Test User");
        request.setMobileNumber("**********");

        assertDoesNotThrow(() -> authService.register(request));
    }

    // ========== Test Data Builders ==========
    static class RegisterRequestBuilder {
        private final RegisterRequest request = new RegisterRequest();
        private RegisterRequestBuilder() {}
        public static RegisterRequestBuilder builder() { return new RegisterRequestBuilder(); }
        public RegisterRequestBuilder email(String email) { request.setEmail(email); return this; }
        public RegisterRequestBuilder name(String name) { request.setName(name); return this; }
        public RegisterRequestBuilder mobileNumber(String mobile) { request.setMobileNumber(mobile); return this; }
        public RegisterRequest build() { return request; }
    }
    static class ForgotPasswordRequestBuilder {
        private final ForgotPasswordRequest request = new ForgotPasswordRequest();
        private ForgotPasswordRequestBuilder() {}
        public static ForgotPasswordRequestBuilder builder() { return new ForgotPasswordRequestBuilder(); }
        public ForgotPasswordRequestBuilder email(String email) { request.setEmail(email); return this; }
        public ForgotPasswordRequestBuilder ipAddress(String ip) { request.setIpAddress(ip); return this; }
        public ForgotPasswordRequestBuilder deviceDetails(String device) { request.setDeviceDetails(device); return this; }
        public ForgotPasswordRequest build() { return request; }
    }
    static class ResetPasswordRequestBuilder {
        private final ResetPasswordRequest request = new ResetPasswordRequest();
        private ResetPasswordRequestBuilder() {}
        public static ResetPasswordRequestBuilder builder() { return new ResetPasswordRequestBuilder(); }
        public ResetPasswordRequestBuilder resetToken(String token) { request.setResetToken(token); return this; }
        public ResetPasswordRequestBuilder newPassword(String pwd) { request.setNewPassword(pwd); return this; }
        public ResetPasswordRequestBuilder ipAddress(String ip) { request.setIpAddress(ip); return this; }
        public ResetPasswordRequestBuilder deviceDetails(String device) { request.setDeviceDetails(device); return this; }
        public ResetPasswordRequest build() { return request; }
    }
    static class LoginRequestBuilder {
        private final LoginRequest request = new LoginRequest();
        private LoginRequestBuilder() {}
        public static LoginRequestBuilder builder() { return new LoginRequestBuilder(); }
        public LoginRequestBuilder email(String email) { request.setEmail(email); return this; }
        public LoginRequestBuilder password(String pwd) { request.setPassword(pwd); return this; }
        public LoginRequestBuilder ipAddress(String ip) { request.setIpAddress(ip); return this; }
        public LoginRequestBuilder deviceDetails(String device) { request.setDeviceDetails(device); return this; }
        public LoginRequestBuilder overrideExistingLogins(boolean val) { request.setOverrideExistingLogins(val); return this; }
        public LoginRequest build() { return request; }
    }
    static class UsersBuilder {
        private final Users user = new Users();
        public UsersBuilder id(UUID id) { user.setId(id); return this; }
        public UsersBuilder email(String email) { user.setEmail(email); return this; }
        public UsersBuilder isActive(boolean active) { user.setIsActive(active); return this; }
        public UsersBuilder emailVerified(boolean verified) { user.setEmailVerified(verified); return this; }
        public UsersBuilder accountLocked(boolean locked) { user.setAccountLocked(locked); return this; }
        public UsersBuilder name(String name) { user.setName(name); return this; }
        public UsersBuilder mobileNumber(String mobile) { user.setMobileNumber(mobile); return this; }
        public Users build() { return user; }
    }
} 