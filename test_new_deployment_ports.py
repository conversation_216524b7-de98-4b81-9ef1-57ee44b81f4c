#!/usr/bin/env python3
"""
Test script to verify that new deployments use the correct container ports
from the payload without any hardcoded defaults overriding them.
"""

import json
import sys
import tempfile
import shutil
from pathlib import Path

# Add scripts directory to path
sys.path.insert(0, str(Path(__file__).parent / 'scripts'))

from process_payload import (
    create_placeholder_mapping, 
    parse_payload, 
    parse_secrets,
    create_processed_manifests
)

def test_new_deployment_workflow():
    """Test the complete new deployment workflow with correct ports"""
    
    print("🧪 Testing New Deployment Workflow")
    print("=" * 60)
    
    # Test cases for different application types
    test_cases = [
        {
            "name": "Django Backend",
            "payload": {
                "project_id": "test-django-new",
                "application_type": "django-backend",
                "docker_image": "test/django-app",
                "docker_tag": "v1.0.0",
                "container_port": 8000,
                "source_repo": "test/django-repo",
                "source_branch": "main",
                "commit_sha": "abc123def456"
            },
            "expected_port": "8000"
        },
        {
            "name": "NestJS Backend",
            "payload": {
                "project_id": "test-nestjs-new",
                "application_type": "nest-backend",
                "docker_image": "test/nestjs-app",
                "docker_tag": "v1.0.0",
                "container_port": 3000,
                "source_repo": "test/nestjs-repo",
                "source_branch": "main",
                "commit_sha": "def456ghi789"
            },
            "expected_port": "3000"
        },
        {
            "name": "Spring Boot Backend",
            "payload": {
                "project_id": "test-spring-new",
                "application_type": "springboot-backend",
                "docker_image": "test/spring-app",
                "docker_tag": "v1.0.0",
                "container_port": 8080,
                "source_repo": "test/spring-repo",
                "source_branch": "main",
                "commit_sha": "ghi789jkl012"
            },
            "expected_port": "8080"
        }
    ]
    
    all_tests_passed = True
    
    for test_case in test_cases:
        print(f"\n📋 Testing: {test_case['name']}")
        print(f"   Expected port: {test_case['expected_port']}")
        
        try:
            # Step 1: Create placeholder mapping
            secrets = parse_secrets("")
            mapping = create_placeholder_mapping(test_case['payload'], secrets)
            
            container_port_value = mapping.get('PLACEHOLDER_CONTAINER_PORT')
            print(f"   Generated PLACEHOLDER_CONTAINER_PORT: {container_port_value}")
            
            if container_port_value != test_case['expected_port']:
                print(f"❌ FAIL: Expected '{test_case['expected_port']}', got '{container_port_value}'")
                all_tests_passed = False
                continue
            
            # Step 2: Test full manifest generation
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # Copy template manifests
                source_manifests = Path("manifests")
                if source_manifests.exists():
                    # Test manifest processing
                    processed_dir = create_processed_manifests(
                        source_manifests,
                        temp_path,
                        mapping,
                        environment="staging",
                        project_id=test_case['payload']['project_id'],
                        application_type=test_case['payload']['application_type']
                    )
                    
                    if processed_dir:
                        print(f"   ✅ Manifests created in: {processed_dir}")
                        
                        # Check deployment file
                        deployment_file = processed_dir / "overlays" / "staging" / "deployment.yaml"
                        if deployment_file.exists():
                            with open(deployment_file, 'r') as f:
                                content = f.read()
                            
                            # Check for placeholders
                            if 'PLACEHOLDER_CONTAINER_PORT' in content:
                                print("   ❌ FAIL: Deployment still contains placeholder!")
                                all_tests_passed = False
                            else:
                                # Check for correct port
                                if f"containerPort: {test_case['expected_port']}" in content:
                                    print(f"   ✅ SUCCESS: Deployment uses correct port {test_case['expected_port']}")
                                else:
                                    print(f"   ❌ FAIL: Deployment doesn't contain expected port {test_case['expected_port']}")
                                    # Show what ports are actually in the file
                                    lines = content.split('\n')
                                    for i, line in enumerate(lines, 1):
                                        if 'containerPort:' in line:
                                            print(f"      Line {i}: {line.strip()}")
                                    all_tests_passed = False
                        else:
                            print("   ❌ FAIL: Deployment file not created!")
                            all_tests_passed = False
                        
                        # Check service file
                        service_file = processed_dir / "overlays" / "staging" / "service.yaml"
                        if service_file.exists():
                            with open(service_file, 'r') as f:
                                content = f.read()
                            
                            if f"port: {test_case['expected_port']}" in content:
                                print(f"   ✅ SUCCESS: Service uses correct port {test_case['expected_port']}")
                            else:
                                print(f"   ❌ FAIL: Service doesn't contain expected port {test_case['expected_port']}")
                                all_tests_passed = False
                        else:
                            print("   ❌ FAIL: Service file not created!")
                            all_tests_passed = False
                    else:
                        print("   ❌ FAIL: Manifest processing failed!")
                        all_tests_passed = False
                else:
                    print("   ❌ FAIL: Source manifests directory not found!")
                    all_tests_passed = False
            
        except Exception as e:
            print(f"   ❌ ERROR: {e}")
            import traceback
            traceback.print_exc()
            all_tests_passed = False
    
    return all_tests_passed

def test_missing_container_port():
    """Test that missing container_port raises appropriate error"""
    
    print("\n🔍 Testing Missing Container Port Validation")
    print("=" * 60)
    
    invalid_payload = {
        "project_id": "test-invalid",
        "application_type": "django-backend",
        "docker_image": "test/invalid-app",
        "docker_tag": "latest",
        # Missing container_port - should raise ValueError
        "source_repo": "test/invalid-repo",
        "source_branch": "main",
        "commit_sha": "invalid123"
    }
    
    print("📋 Testing payload without container_port...")
    
    try:
        secrets = parse_secrets("")
        mapping = create_placeholder_mapping(invalid_payload, secrets)
        print("❌ FAIL: Should have raised ValueError for missing container_port")
        return False
    except ValueError as e:
        if "container_port is a required field" in str(e):
            print("✅ SUCCESS: Correctly raised ValueError for missing container_port")
            return True
        else:
            print(f"❌ FAIL: Unexpected ValueError message: {e}")
            return False
    except Exception as e:
        print(f"❌ FAIL: Unexpected error type: {e}")
        return False

def main():
    """Run all tests"""
    
    print("🚀 Testing Container Port Fix for New Deployments")
    print("=" * 80)
    print("This test verifies that new deployments use the correct container ports")
    print("from the payload without any hardcoded defaults overriding them.")
    print()
    
    # Test the workflow
    workflow_passed = test_new_deployment_workflow()
    
    # Test validation
    validation_passed = test_missing_container_port()
    
    print("\n" + "=" * 80)
    print("🏁 Test Results")
    print(f"   Workflow Tests: {'✅ PASSED' if workflow_passed else '❌ FAILED'}")
    print(f"   Validation Tests: {'✅ PASSED' if validation_passed else '❌ FAILED'}")
    
    if workflow_passed and validation_passed:
        print("\n🎉 ALL TESTS PASSED!")
        print("   ✅ Django apps will use port 8000 from payload")
        print("   ✅ NestJS apps will use port 3000 from payload")
        print("   ✅ Spring Boot apps will use port 8080 from payload")
        print("   ✅ Missing container_port is properly validated")
        return True
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("   The container port issue may not be fully resolved.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
