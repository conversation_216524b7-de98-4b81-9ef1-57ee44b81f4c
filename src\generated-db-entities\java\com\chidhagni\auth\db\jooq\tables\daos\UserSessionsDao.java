/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.auth.db.jooq.tables.daos;


import com.chidhagni.auth.db.jooq.tables.UserSessions;
import com.chidhagni.auth.db.jooq.tables.records.UserSessionsRecord;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class UserSessionsDao extends DAOImpl<UserSessionsRecord, com.chidhagni.auth.db.jooq.tables.pojos.UserSessions, UUID> {

    /**
     * Create a new UserSessionsDao without any configuration
     */
    public UserSessionsDao() {
        super(UserSessions.USER_SESSIONS, com.chidhagni.auth.db.jooq.tables.pojos.UserSessions.class);
    }

    /**
     * Create a new UserSessionsDao with an attached configuration
     */
    @Autowired
    public UserSessionsDao(Configuration configuration) {
        super(UserSessions.USER_SESSIONS, com.chidhagni.auth.db.jooq.tables.pojos.UserSessions.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.auth.db.jooq.tables.pojos.UserSessions object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(UserSessions.USER_SESSIONS.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchById(UUID... values) {
        return fetch(UserSessions.USER_SESSIONS.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.auth.db.jooq.tables.pojos.UserSessions fetchOneById(UUID value) {
        return fetchOne(UserSessions.USER_SESSIONS.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchOptionalById(UUID value) {
        return fetchOptional(UserSessions.USER_SESSIONS.ID, value);
    }

    /**
     * Fetch records that have <code>user_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchRangeOfUserId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(UserSessions.USER_SESSIONS.USER_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>user_id IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchByUserId(UUID... values) {
        return fetch(UserSessions.USER_SESSIONS.USER_ID, values);
    }

    /**
     * Fetch records that have <code>session_token BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchRangeOfSessionToken(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserSessions.USER_SESSIONS.SESSION_TOKEN, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>session_token IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchBySessionToken(String... values) {
        return fetch(UserSessions.USER_SESSIONS.SESSION_TOKEN, values);
    }

    /**
     * Fetch a unique record that has <code>session_token = value</code>
     */
    public com.chidhagni.auth.db.jooq.tables.pojos.UserSessions fetchOneBySessionToken(String value) {
        return fetchOne(UserSessions.USER_SESSIONS.SESSION_TOKEN, value);
    }

    /**
     * Fetch a unique record that has <code>session_token = value</code>
     */
    public Optional<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchOptionalBySessionToken(String value) {
        return fetchOptional(UserSessions.USER_SESSIONS.SESSION_TOKEN, value);
    }

    /**
     * Fetch records that have <code>refresh_token BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchRangeOfRefreshToken(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserSessions.USER_SESSIONS.REFRESH_TOKEN, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>refresh_token IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchByRefreshToken(String... values) {
        return fetch(UserSessions.USER_SESSIONS.REFRESH_TOKEN, values);
    }

    /**
     * Fetch a unique record that has <code>refresh_token = value</code>
     */
    public com.chidhagni.auth.db.jooq.tables.pojos.UserSessions fetchOneByRefreshToken(String value) {
        return fetchOne(UserSessions.USER_SESSIONS.REFRESH_TOKEN, value);
    }

    /**
     * Fetch a unique record that has <code>refresh_token = value</code>
     */
    public Optional<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchOptionalByRefreshToken(String value) {
        return fetchOptional(UserSessions.USER_SESSIONS.REFRESH_TOKEN, value);
    }

    /**
     * Fetch records that have <code>expires_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchRangeOfExpiresAt(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(UserSessions.USER_SESSIONS.EXPIRES_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>expires_at IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchByExpiresAt(LocalDateTime... values) {
        return fetch(UserSessions.USER_SESSIONS.EXPIRES_AT, values);
    }

    /**
     * Fetch records that have <code>refresh_expires_at BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchRangeOfRefreshExpiresAt(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(UserSessions.USER_SESSIONS.REFRESH_EXPIRES_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>refresh_expires_at IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchByRefreshExpiresAt(LocalDateTime... values) {
        return fetch(UserSessions.USER_SESSIONS.REFRESH_EXPIRES_AT, values);
    }

    /**
     * Fetch records that have <code>is_active BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchRangeOfIsActive(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(UserSessions.USER_SESSIONS.IS_ACTIVE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_active IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchByIsActive(Boolean... values) {
        return fetch(UserSessions.USER_SESSIONS.IS_ACTIVE, values);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchRangeOfCreatedAt(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(UserSessions.USER_SESSIONS.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchByCreatedAt(LocalDateTime... values) {
        return fetch(UserSessions.USER_SESSIONS.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>last_accessed_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchRangeOfLastAccessedAt(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(UserSessions.USER_SESSIONS.LAST_ACCESSED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>last_accessed_at IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchByLastAccessedAt(LocalDateTime... values) {
        return fetch(UserSessions.USER_SESSIONS.LAST_ACCESSED_AT, values);
    }

    /**
     * Fetch records that have <code>logged_out_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchRangeOfLoggedOutAt(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(UserSessions.USER_SESSIONS.LOGGED_OUT_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>logged_out_at IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchByLoggedOutAt(LocalDateTime... values) {
        return fetch(UserSessions.USER_SESSIONS.LOGGED_OUT_AT, values);
    }

    /**
     * Fetch records that have <code>ip_address BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchRangeOfIpAddress(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserSessions.USER_SESSIONS.IP_ADDRESS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ip_address IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchByIpAddress(String... values) {
        return fetch(UserSessions.USER_SESSIONS.IP_ADDRESS, values);
    }

    /**
     * Fetch records that have <code>device_details BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchRangeOfDeviceDetails(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserSessions.USER_SESSIONS.DEVICE_DETAILS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>device_details IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserSessions> fetchByDeviceDetails(String... values) {
        return fetch(UserSessions.USER_SESSIONS.DEVICE_DETAILS, values);
    }
}
