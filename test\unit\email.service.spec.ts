// WARNING: All infrastructure dependencies (DB, email, external services) must be mocked using jest.fn(). No real calls allowed in unit tests.
import { Test, TestingModule } from '@nestjs/testing';
import { EmailService } from '../../src/email/email.service';
import { MailerService } from '@nestjs-modules/mailer';
import { ConfigService } from '@nestjs/config';

describe('EmailService', () => {
  let service: EmailService;
  let configService: jest.Mocked<ConfigService>;
  let mailerService: jest.Mocked<MailerService>;
  let consoleSpy: jest.SpyInstance;
  let consoleErrorSpy: jest.SpyInstance;

  beforeEach(async () => {
    // Set test environment for mock email tests
    process.env.NODE_ENV = 'test';
    
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailService,
        {
          provide: MailerService,
          useValue: {
            sendMail: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<EmailService>(EmailService);
    configService = module.get(ConfigService);
    mailerService = module.get(MailerService);
    consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
  });

  afterEach(() => {
    consoleSpy.mockRestore();
    consoleErrorSpy.mockRestore();
  });

  describe('when SMTP is not configured (test mode)', () => {
    it('should log verification email when SMTP is not configured', async () => {
      await service.sendVerificationLink('<EMAIL>', 'test-token');
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('[MOCK EMAIL]'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('To: <EMAIL>'));
    });

    it('should log password reset email when SMTP is not configured', async () => {
      await service.sendPasswordResetLink('<EMAIL>', 'test-token');
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('[MOCK EMAIL]'));
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('To: <EMAIL>'));
    });
  });

  describe('with configured SMTP', () => {
    beforeEach(async () => {
      // Set environment to 'smtp' for configured SMTP tests
      process.env.NODE_ENV = 'smtp';
      
      // Mock config service to return SMTP credentials
      configService.get.mockImplementation((key: string) => {
        switch (key) {
          case 'email.host': return 'smtp.gmail.com';
          case 'email.port': return '587';
          case 'email.user': return '<EMAIL>';
          case 'email.pass': return 'testpass';
          case 'email.from': return '<EMAIL>';
          case 'APP_URL': return 'http://localhost:3000';
          default: return undefined;
        }
      });

      // Recreate service with new environment
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          EmailService,
          {
            provide: MailerService,
            useValue: {
              sendMail: jest.fn(),
            },
          },
          {
            provide: ConfigService,
            useValue: configService,
          },
        ],
      }).compile();

      service = module.get<EmailService>(EmailService);
      mailerService = module.get(MailerService);
    });

    it('should send a verification email', async () => {
      (mailerService.sendMail as jest.Mock).mockResolvedValue(true);
      await service.sendVerificationLink('<EMAIL>', 'test-token');
      expect(mailerService.sendMail).toHaveBeenCalled();
    });

    it.each([
      [false, 'Failed to send email'],
      [() => { throw new Error('timeout'); }, 'timeout'],
    ])('should throw for sendMail failure or timeout: %p', async (sendMailImpl, expectedError) => {
      if (typeof sendMailImpl === 'function') {
        (mailerService.sendMail as jest.Mock).mockImplementation(sendMailImpl);
      } else {
        (mailerService.sendMail as jest.Mock).mockResolvedValue(sendMailImpl);
      }
      await expect(service.sendVerificationLink('<EMAIL>', 'test-token')).rejects.toThrow(expectedError);
    }, 15000);
  });

  describe('sendPasswordResetLink', () => {
    describe('with configured SMTP', () => {
      beforeEach(async () => {
        // Set environment to 'smtp' for configured SMTP tests
        process.env.NODE_ENV = 'smtp';
        
        // Mock config service to return SMTP credentials
        configService.get.mockImplementation((key: string) => {
          switch (key) {
            case 'email.host': return 'smtp.gmail.com';
            case 'email.port': return '587';
            case 'email.user': return '<EMAIL>';
            case 'email.pass': return 'testpass';
            case 'email.from': return '<EMAIL>';
            case 'APP_URL': return 'http://localhost:3000';
            default: return undefined;
          }
        });

        // Recreate service with new environment
        const module: TestingModule = await Test.createTestingModule({
          providers: [
            EmailService,
            {
              provide: MailerService,
              useValue: {
                sendMail: jest.fn(),
              },
            },
            {
              provide: ConfigService,
              useValue: configService,
            },
          ],
        }).compile();

        service = module.get<EmailService>(EmailService);
        mailerService = module.get(MailerService);
      });

      it('should send a password reset email', async () => {
        (mailerService.sendMail as jest.Mock).mockResolvedValue(true);
        await service.sendPasswordResetLink('<EMAIL>', 'test-token');
        expect(mailerService.sendMail).toHaveBeenCalled();
      });

      it.each([
        [false, 'Failed to send email'],
        [() => { throw new Error('timeout'); }, 'timeout'],
      ])('should throw for sendMail failure or timeout: %p', async (sendMailImpl, expectedError) => {
        if (typeof sendMailImpl === 'function') {
          (mailerService.sendMail as jest.Mock).mockImplementation(sendMailImpl);
        } else {
          (mailerService.sendMail as jest.Mock).mockResolvedValue(sendMailImpl);
        }
        await expect(service.sendPasswordResetLink('<EMAIL>', 'test-token')).rejects.toThrow(expectedError);
      }, 15000);
    });
  });

  describe('validation', () => {
    it('should throw BadRequestException for invalid email', async () => {
      await expect(service.sendVerificationLink('invalid-email', 'token')).rejects.toThrow('Invalid email address');
    });

    it('should throw BadRequestException for missing token', async () => {
      await expect(service.sendVerificationLink('<EMAIL>', '')).rejects.toThrow('Token is required');
    });
  });

  describe('getEmailProviderStatus', () => {
    it('should return status when no provider is configured', () => {
      const status = service.getEmailProviderStatus();
      expect(status).toEqual({
        totalProviders: 0,
        providers: [],
        currentProvider: 'None',
        configured: false,
      });
    });

    it('should return status when provider is configured', async () => {
      // Set environment to 'smtp' and recreate service
      process.env.NODE_ENV = 'smtp';
      configService.get.mockImplementation((key: string) => {
        switch (key) {
          case 'email.host': return 'smtp.gmail.com';
          case 'email.port': return '587';
          case 'email.user': return '<EMAIL>';
          case 'email.pass': return 'testpass';
          case 'email.from': return '<EMAIL>';
          default: return undefined;
        }
      });

      const module: TestingModule = await Test.createTestingModule({
        providers: [
          EmailService,
          {
            provide: MailerService,
            useValue: { sendMail: jest.fn() },
          },
          {
            provide: ConfigService,
            useValue: configService,
          },
        ],
      }).compile();

      const configuredService = module.get<EmailService>(EmailService);
      const status = configuredService.getEmailProviderStatus();
      expect(status).toEqual({
        totalProviders: 1,
        providers: [{
          name: 'Gmail',
          usage: '0/500',
          available: true,
        }],
        currentProvider: 'Gmail',
        configured: true,
      });
    });
  });
}); 