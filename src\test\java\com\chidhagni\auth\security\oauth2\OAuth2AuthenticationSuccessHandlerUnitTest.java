package com.chidhagni.auth.security.oauth2;

import com.chidhagni.auth.config.AppProperties;
import com.chidhagni.auth.exception.BadRequestException;
import com.chidhagni.auth.security.TokenProvider;
import com.chidhagni.auth.security.UserPrincipal;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.RedirectStrategy;

import java.util.Collections;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class OAuth2AuthenticationSuccessHandlerUnitTest {
    
    @Mock
    private TokenProvider tokenProvider;
    
    @Mock
    private AppProperties appProperties;
    
    @Mock
    private HttpCookieOAuth2AuthorizationRequestRepository repo;
    
    @Mock
    private HttpServletRequest request;
    
    @Mock
    private HttpServletResponse response;
    
    @Mock
    private Authentication authentication;
    
    @Mock
    private UserPrincipal userPrincipal;
    
    @Mock
    private AppProperties.OAuth2 oauth2;
    
    @Mock
    private RedirectStrategy redirectStrategy;
    
    private OAuth2AuthenticationSuccessHandler handler;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        handler = new OAuth2AuthenticationSuccessHandler(tokenProvider, appProperties, repo);
        handler.setRedirectStrategy(redirectStrategy);
        
        // Setup common mocks
        when(appProperties.getOauth2()).thenReturn(oauth2);
        when(authentication.getPrincipal()).thenReturn(userPrincipal);
        when(response.isCommitted()).thenReturn(false);
    }

    @Test
    void testOnAuthenticationSuccess_Success() throws Exception {
        // Given
        UUID userId = UUID.randomUUID();
        String token = "test-token";
        String refreshToken = "test-refresh-token";
        String expiresAt = "2025-07-03T19:36:35Z";
        String refreshExpiresAt = "2025-07-04T11:36:35Z";
        String targetUrl = "http://localhost:3000/callback?sessionToken=" + token + "&refreshToken=" + refreshToken + "&userId=" + userId + "&expiresAt=" + expiresAt + "&refreshExpiresAt=" + refreshExpiresAt;
        
        when(userPrincipal.getId()).thenReturn(userId);
        when(tokenProvider.createToken(userId)).thenReturn(token);
        when(tokenProvider.createRefreshToken(userId)).thenReturn(refreshToken);
        when(tokenProvider.getTokenExpiry(token)).thenReturn(expiresAt);
        when(tokenProvider.getTokenExpiry(refreshToken)).thenReturn(refreshExpiresAt);
        when(oauth2.getDefaultTargetUrl()).thenReturn("http://localhost:3000/callback");
        
        // When
        handler.onAuthenticationSuccess(request, response, authentication);
        
        // Then
        verify(repo).removeAuthorizationRequestCookies(request, response);
        verify(redirectStrategy).sendRedirect(request, response, targetUrl);
    }

    @Test
    void testOnAuthenticationSuccess_ResponseAlreadyCommitted() throws Exception {
        // Given
        when(response.isCommitted()).thenReturn(true);
        UUID userId = UUID.randomUUID();
        String token = "test-token";
        String defaultUrl = "http://localhost:8080/default";
        when(userPrincipal.getId()).thenReturn(userId);
        when(tokenProvider.createToken(userId)).thenReturn(token);
        when(request.getCookies()).thenReturn(null);
        when(oauth2.getDefaultTargetUrl()).thenReturn(defaultUrl);

        // When
        handler.onAuthenticationSuccess(request, response, authentication);

        // Then
        verify(repo, never()).removeAuthorizationRequestCookies(any(), any());
        verify(redirectStrategy, never()).sendRedirect(any(), any(), any());
    }

    @Test
    void testDetermineTargetUrl_WithValidRedirectUri() {
        // Given
        UUID userId = UUID.randomUUID();
        String token = "test-token";
        String refreshToken = "test-refresh-token";
        String expiresAt = "2025-07-03T19:36:35Z";
        String refreshExpiresAt = "2025-07-04T11:36:35Z";
        String redirectUri = "http://localhost:3000/callback";
        Cookie cookie = new Cookie("redirect_uri", redirectUri); // nosemgrep
        
        when(userPrincipal.getId()).thenReturn(userId);
        when(tokenProvider.createToken(userId)).thenReturn(token);
        when(tokenProvider.createRefreshToken(userId)).thenReturn(refreshToken);
        when(tokenProvider.getTokenExpiry(token)).thenReturn(expiresAt);
        when(tokenProvider.getTokenExpiry(refreshToken)).thenReturn(refreshExpiresAt);
        when(request.getCookies()).thenReturn(new Cookie[]{cookie});
        when(oauth2.getAuthorizedRedirectUris()).thenReturn(Collections.singletonList("http://localhost:3000/callback"));
        
        // When
        String result = handler.determineTargetUrl(request, response, authentication);
        
        // Then
        assertTrue(result.contains("sessionToken=" + token));
        assertTrue(result.contains("refreshToken=" + refreshToken));
        assertTrue(result.contains("userId=" + userId));
        assertTrue(result.contains("expiresAt=" + expiresAt));
        assertTrue(result.contains("refreshExpiresAt=" + refreshExpiresAt));
        assertTrue(result.startsWith(redirectUri));
    }

    @Test
    void testDetermineTargetUrl_WithDefaultTargetUrl() {
        // Given
        UUID userId = UUID.randomUUID();
        String token = "test-token";
        String refreshToken = "test-refresh-token";
        String expiresAt = "2025-07-03T19:36:35Z";
        String refreshExpiresAt = "2025-07-04T11:36:35Z";
        String defaultUrl = "http://localhost:8080/default";
        
        when(userPrincipal.getId()).thenReturn(userId);
        when(tokenProvider.createToken(userId)).thenReturn(token);
        when(tokenProvider.createRefreshToken(userId)).thenReturn(refreshToken);
        when(tokenProvider.getTokenExpiry(token)).thenReturn(expiresAt);
        when(tokenProvider.getTokenExpiry(refreshToken)).thenReturn(refreshExpiresAt);
        when(request.getCookies()).thenReturn(null);
        when(oauth2.getDefaultTargetUrl()).thenReturn(defaultUrl);
        
        // When
        String result = handler.determineTargetUrl(request, response, authentication);
        
        // Then
        assertTrue(result.contains("sessionToken=" + token));
        assertTrue(result.contains("refreshToken=" + refreshToken));
        assertTrue(result.contains("userId=" + userId));
        assertTrue(result.contains("expiresAt=" + expiresAt));
        assertTrue(result.contains("refreshExpiresAt=" + refreshExpiresAt));
        assertTrue(result.startsWith(defaultUrl));
    }

    @Test
    void testDetermineTargetUrl_WithEmptyCookies() {
        // Given
        UUID userId = UUID.randomUUID();
        String token = "test-token";
        String refreshToken = "test-refresh-token";
        String expiresAt = "2025-07-03T19:36:35Z";
        String refreshExpiresAt = "2025-07-04T11:36:35Z";
        String defaultUrl = "http://localhost:8080/default";
        
        when(userPrincipal.getId()).thenReturn(userId);
        when(tokenProvider.createToken(userId)).thenReturn(token);
        when(tokenProvider.createRefreshToken(userId)).thenReturn(refreshToken);
        when(tokenProvider.getTokenExpiry(token)).thenReturn(expiresAt);
        when(tokenProvider.getTokenExpiry(refreshToken)).thenReturn(refreshExpiresAt);
        when(request.getCookies()).thenReturn(new Cookie[0]);
        when(oauth2.getDefaultTargetUrl()).thenReturn(defaultUrl);
        
        // When
        String result = handler.determineTargetUrl(request, response, authentication);
        
        // Then
        assertTrue(result.contains("sessionToken=" + token));
        assertTrue(result.contains("refreshToken=" + refreshToken));
        assertTrue(result.contains("userId=" + userId));
        assertTrue(result.contains("expiresAt=" + expiresAt));
        assertTrue(result.contains("refreshExpiresAt=" + refreshExpiresAt));
        assertTrue(result.startsWith(defaultUrl));
    }

    @Test
    void testDetermineTargetUrl_WithDifferentCookie() {
        // Given
        UUID userId = UUID.randomUUID();
        String token = "test-token";
        String refreshToken = "test-refresh-token";
        String expiresAt = "2025-07-03T19:36:35Z";
        String refreshExpiresAt = "2025-07-04T11:36:35Z";
        String defaultUrl = "http://localhost:8080/default";
        Cookie cookie = new Cookie("different-cookie", "value"); // nosemgrep
        
        when(userPrincipal.getId()).thenReturn(userId);
        when(tokenProvider.createToken(userId)).thenReturn(token);
        when(tokenProvider.createRefreshToken(userId)).thenReturn(refreshToken);
        when(tokenProvider.getTokenExpiry(token)).thenReturn(expiresAt);
        when(tokenProvider.getTokenExpiry(refreshToken)).thenReturn(refreshExpiresAt);
        when(request.getCookies()).thenReturn(new Cookie[]{cookie});
        when(oauth2.getDefaultTargetUrl()).thenReturn(defaultUrl);
        
        // When
        String result = handler.determineTargetUrl(request, response, authentication);
        
        // Then
        assertTrue(result.contains("sessionToken=" + token));
        assertTrue(result.contains("refreshToken=" + refreshToken));
        assertTrue(result.contains("userId=" + userId));
        assertTrue(result.contains("expiresAt=" + expiresAt));
        assertTrue(result.contains("refreshExpiresAt=" + refreshExpiresAt));
        assertTrue(result.startsWith(defaultUrl));
    }

    @Test
    void testDetermineTargetUrl_WithUnauthorizedRedirectUri() {
        // Given
        String redirectUri = "http://malicious-site.com/callback";
        Cookie cookie = new Cookie("redirect_uri", redirectUri); // nosemgrep
        
        when(request.getCookies()).thenReturn(new Cookie[]{cookie});
        when(oauth2.getAuthorizedRedirectUris()).thenReturn(Collections.singletonList("http://localhost:3000/callback"));
        
        // When & Then
        BadRequestException exception = assertThrows(BadRequestException.class, () -> {
            handler.determineTargetUrl(request, response, authentication);
        });
        
        assertTrue(exception.getMessage().contains("Unauthorized Redirect URI"));
    }

    @Test
    void testDetermineTargetUrl_WithAuthorizedRedirectUri_DifferentPort() {
        // Given
        String redirectUri = "http://localhost:3001/callback";
        Cookie cookie = new Cookie("redirect_uri", redirectUri); // nosemgrep
        
        when(request.getCookies()).thenReturn(new Cookie[]{cookie});
        when(oauth2.getAuthorizedRedirectUris()).thenReturn(Collections.singletonList("http://localhost:3000/callback"));
        
        // When & Then
        BadRequestException exception = assertThrows(BadRequestException.class, () -> {
            handler.determineTargetUrl(request, response, authentication);
        });
        
        assertTrue(exception.getMessage().contains("Unauthorized Redirect URI"));
    }

    @Test
    void testDetermineTargetUrl_WithAuthorizedRedirectUri_SameHostDifferentPort() {
        // Given
        String redirectUri = "http://localhost:3000/callback";
        Cookie cookie = new Cookie("redirect_uri", redirectUri); // nosemgrep
        UUID userId = UUID.randomUUID();
        String token = "test-token";
        String refreshToken = "test-refresh-token";
        String expiresAt = "2025-07-03T19:36:35Z";
        String refreshExpiresAt = "2025-07-04T11:36:35Z";
        
        when(userPrincipal.getId()).thenReturn(userId);
        when(tokenProvider.createToken(userId)).thenReturn(token);
        when(tokenProvider.createRefreshToken(userId)).thenReturn(refreshToken);
        when(tokenProvider.getTokenExpiry(token)).thenReturn(expiresAt);
        when(tokenProvider.getTokenExpiry(refreshToken)).thenReturn(refreshExpiresAt);
        when(request.getCookies()).thenReturn(new Cookie[]{cookie});
        when(oauth2.getAuthorizedRedirectUris()).thenReturn(Collections.singletonList("http://localhost:3000/callback"));
        
        // When
        String result = handler.determineTargetUrl(request, response, authentication);
        
        // Then
        assertTrue(result.contains("sessionToken=" + token));
        assertTrue(result.contains("refreshToken=" + refreshToken));
        assertTrue(result.contains("userId=" + userId));
        assertTrue(result.contains("expiresAt=" + expiresAt));
        assertTrue(result.contains("refreshExpiresAt=" + refreshExpiresAt));
        assertTrue(result.startsWith(redirectUri));
    }

    @Test
    void testDetermineTargetUrl_WithAuthorizedRedirectUri_DifferentHost() {
        // Given
        String redirectUri = "http://malicious-site.com:3000/callback";
        Cookie cookie = new Cookie("redirect_uri", redirectUri); // nosemgrep
        
        when(request.getCookies()).thenReturn(new Cookie[]{cookie});
        when(oauth2.getAuthorizedRedirectUris()).thenReturn(Collections.singletonList("http://localhost:3000/callback"));
        
        // When & Then
        BadRequestException exception = assertThrows(BadRequestException.class, () -> {
            handler.determineTargetUrl(request, response, authentication);
        });
        
        assertTrue(exception.getMessage().contains("Unauthorized Redirect URI"));
    }

    @Test
    void testDetermineTargetUrl_WithMultipleAuthorizedUris() {
        // Given
        String redirectUri = "http://localhost:3000/callback";
        Cookie cookie = new Cookie("redirect_uri", redirectUri); // nosemgrep
        UUID userId = UUID.randomUUID();
        String token = "test-token";
        
        when(userPrincipal.getId()).thenReturn(userId);
        when(tokenProvider.createToken(userId)).thenReturn(token);
        when(request.getCookies()).thenReturn(new Cookie[]{cookie});
        when(oauth2.getAuthorizedRedirectUris()).thenReturn(
            Collections.singletonList("http://localhost:3000/callback")
        );
        
        // When
        String result = handler.determineTargetUrl(request, response, authentication);
        
        // Then
        assertTrue(result.contains("sessionToken=" + token));
        assertTrue(result.startsWith(redirectUri));
    }

    @Test
    void testClearAuthenticationAttributes() {
        // Given
        Cookie cookie = new Cookie("redirect_uri", "http://localhost:3000/callback"); // nosemgrep
        when(request.getCookies()).thenReturn(new Cookie[]{cookie});
        
        // When
        handler.clearAuthenticationAttributes(request, response);
        
        // Then
        verify(repo).removeAuthorizationRequestCookies(request, response);
    }
} 