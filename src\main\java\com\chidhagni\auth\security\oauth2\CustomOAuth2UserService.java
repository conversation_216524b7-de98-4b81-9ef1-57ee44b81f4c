package com.chidhagni.auth.security.oauth2;

import com.chidhagni.auth.constants.AuthProvider;
import com.chidhagni.auth.db.jooq.tables.daos.UserVerificationDao;
import com.chidhagni.auth.db.jooq.tables.pojos.UserSessions;
import com.chidhagni.auth.db.jooq.tables.pojos.UserVerification;
import com.chidhagni.auth.db.jooq.tables.pojos.Users;
import com.chidhagni.auth.security.UserPrincipal;
import com.chidhagni.auth.security.oauth2.user.OAuth2UserInfo;
import com.chidhagni.auth.security.oauth2.user.OAuth2UserInfoFactory;
import com.chidhagni.auth.service.AuthService;
import com.chidhagni.auth.db.jooq.tables.daos.UserSessionsDao;
import com.chidhagni.auth.db.jooq.tables.records.UserSessionsRecord;
import com.chidhagni.auth.util.TokenUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.oauth2.client.userinfo.DefaultOAuth2UserService;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Optional;
import java.util.UUID;

@Service
@Slf4j
public class CustomOAuth2UserService extends DefaultOAuth2UserService {

    @Autowired
    private AuthService authService;

    @Autowired
    private UserVerificationDao userVerificationDao;

    @Autowired
    private UserSessionsDao userSessionsDao;

    @Override
    public OAuth2User loadUser(OAuth2UserRequest oAuth2UserRequest) throws OAuth2AuthenticationException {
        log.info("Loading OAuth2 user for registrationId: {}", oAuth2UserRequest.getClientRegistration().getRegistrationId());
        OAuth2User oAuth2User = super.loadUser(oAuth2UserRequest);

        try {
            OAuth2User returnedUser = processOAuth2User(oAuth2UserRequest, oAuth2User);
            log.info("OAuth2User returned from loadUser: {}", returnedUser);
            return returnedUser;
        } catch (Exception ex) {
            log.warn("Exception in processing OAuth2 user: {}", ex.getMessage());
            throw new InternalAuthenticationServiceException(ex.getMessage(), ex.getCause());
        }
    }

    private OAuth2User processOAuth2User(OAuth2UserRequest oAuth2UserRequest, OAuth2User oAuth2User) {
        log.info("Processing OAuth2 user for email: {}", (String) oAuth2User.getAttribute("email"));
        OAuth2UserInfo oAuth2UserInfo = OAuth2UserInfoFactory.getOAuth2UserInfo(
            oAuth2UserRequest.getClientRegistration().getRegistrationId(), 
            oAuth2User.getAttributes()
        );

        validateOAuth2UserInfo(oAuth2UserInfo);

        Optional<Users> userOptional = authService.findUserByEmail(oAuth2UserInfo.getEmail());
        Users user;
        
        if (userOptional.isPresent()) {
            log.info("Existing user found for email: {}", oAuth2UserInfo.getEmail());
            user = handleExistingUser(userOptional.get(), oAuth2UserRequest, oAuth2UserInfo);
        } else {
            log.info("Registering new OAuth2 user for email: {}", oAuth2UserInfo.getEmail());
            user = registerNewUser(oAuth2UserRequest, oAuth2UserInfo);
        }

        // Create a session for OAuth2 login
        createOAuth2UserSession(user.getId());

        return UserPrincipal.create(user, oAuth2User.getAttributes());
    }

    private void validateOAuth2UserInfo(OAuth2UserInfo oAuth2UserInfo) {
        String email = oAuth2UserInfo.getEmail();
        if (email == null || email.isEmpty()) {
            log.warn("Email not found from OAuth2 provider");
            throw new OAuth2AuthenticationProcessingException("Email not found from OAuth2 provider");
        }
    }

    private Users handleExistingUser(Users existingUser, OAuth2UserRequest oAuth2UserRequest, OAuth2UserInfo oAuth2UserInfo) {
        validateProviderMatch(existingUser, oAuth2UserRequest);
        return updateExistingUser(existingUser, oAuth2UserInfo);
    }

    private void validateProviderMatch(Users existingUser, OAuth2UserRequest oAuth2UserRequest) {
        String currentProvider = existingUser.getSocialLoginProvider();
        String requestedProvider = AuthProvider.valueOf(oAuth2UserRequest.getClientRegistration().getRegistrationId()).toString();
        
        if (currentProvider != null && !currentProvider.equals(requestedProvider)) {
            log.warn("Provider mismatch: user signed up with {} but tried to login with {}", currentProvider, requestedProvider);
            throw new OAuth2AuthenticationProcessingException("Looks like you're signed up with " +
                currentProvider + " account. Please use your " + currentProvider + " account to login.");
        }
    }

    private Users registerNewUser(OAuth2UserRequest oAuth2UserRequest, OAuth2UserInfo oAuth2UserInfo) {
        log.info("Registering new user from OAuth2 info: {}", oAuth2UserInfo.getEmail());
        Users user = createUserFromOAuth2Info(oAuth2UserRequest, oAuth2UserInfo);
        createUserVerification(oAuth2UserInfo);
        return authService.createOAuth2User(user);
    }

    private Users createUserFromOAuth2Info(OAuth2UserRequest oAuth2UserRequest, OAuth2UserInfo oAuth2UserInfo) {
        Users user = new Users();
        user.setId(UUID.randomUUID());
        user.setSocialLoginProvider(AuthProvider.valueOf(oAuth2UserRequest.getClientRegistration().getRegistrationId()).toString());
        user.setSocialLoginProviderId(oAuth2UserInfo.getId());
        user.setName(oAuth2UserInfo.getName());
        user.setEmail(oAuth2UserInfo.getEmail());
        user.setSocialLoginProviderImageUrl(oAuth2UserInfo.getImageUrl());
        user.setEmailVerified(true);
        user.setIsActive(true);
        user.setAccountLocked(false);
        user.setFailedLoginAttempts(0);
        user.setPassword(null); // No password for OAuth2 users
        user.setMobileNumber("**********"); // Default mobile for OAuth2 users
        return user;
    }

    private void createUserVerification(OAuth2UserInfo oAuth2UserInfo) {
        UserVerification verification = new UserVerification();
        LocalDateTime now = LocalDateTime.now(ZoneOffset.UTC);
        
        verification.setId(UUID.randomUUID());
        verification.setContactValue(oAuth2UserInfo.getEmail());
        verification.setVerificationToken(UUID.randomUUID().toString());
        verification.setExpiresAt(now.plusYears(1));
        verification.setVerified(true);
        verification.setCreatedAt(now);
        verification.setVerifiedAt(now);
        verification.setIpAddress(null);
        verification.setDeviceDetails(null);
        verification.setName(oAuth2UserInfo.getName());
        verification.setMobileNumber("**********");
        
        userVerificationDao.insert(verification);
    }

    private Users updateExistingUser(Users existingUser, OAuth2UserInfo oAuth2UserInfo) {
        log.info("Updating existing OAuth2 user: {}", existingUser.getEmail());
        existingUser.setName(oAuth2UserInfo.getName());
        existingUser.setSocialLoginProviderImageUrl(oAuth2UserInfo.getImageUrl());
        return authService.updateOAuth2User(existingUser);
    }

    private void createOAuth2UserSession(UUID userId) {
        String sessionToken = TokenUtil.generateToken(64);
        String refreshToken = TokenUtil.generateToken(64);
        LocalDateTime now = LocalDateTime.now(ZoneOffset.UTC);
        LocalDateTime expiresAt = now.plusHours(24); // or use your AuthConstants
        LocalDateTime refreshExpiresAt = now.plusDays(7);
        UserSessions session = new UserSessions();
        session.setId(UUID.randomUUID());
        session.setUserId(userId);
        session.setSessionToken(sessionToken);
        session.setRefreshToken(refreshToken);
        session.setExpiresAt(expiresAt);
        session.setRefreshExpiresAt(refreshExpiresAt);
        session.setIsActive(true);
        session.setCreatedAt(now);
        session.setLastAccessedAt(now);
        session.setIpAddress(null);
        session.setDeviceDetails(null);
        userSessionsDao.insert(session);
    }
} 