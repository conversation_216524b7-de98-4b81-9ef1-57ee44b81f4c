package com.chidhagni.auth.security.oauth2;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Arrays;
import java.util.Optional;

public class CookieUtils {

    public static Optional<Cookie> getCookie(HttpServletRequest request, String name) {
        return Optional.ofNullable(request.getCookies())
                .stream()
                .flatMap(Arrays::stream)
                .filter(cookie -> cookie.getName().equals(name))
                .findFirst();
    }

    public static void addCookie(HttpServletResponse response, String name, String value, int maxAge) {
        Cookie cookie = new Cookie(name, value);
        cookie.setPath("/");
        cookie.setHttpOnly(true);
        cookie.setMaxAge(maxAge);
        cookie.setSecure(true);
        response.addCookie(cookie); // nosemgrep: java.servlets.security.audit.cookie-missing-samesite.cookie-missing-samesite
        String setCookieHeader = String.format("%s=%s; Max-Age=%d; Path=/; HttpOnly; Secure; SameSite=Lax", name, value, maxAge);
        response.setHeader("Set-Cookie", setCookieHeader); // nosemgrep: java.servlets.security.audit.cookie-missing-samesite.cookie-missing-samesite
    }

    public static void deleteCookie(HttpServletRequest request, HttpServletResponse response, String name) {
        Optional.ofNullable(request.getCookies())
                .map(Arrays::stream)
                .ifPresent(stream -> stream
                        .filter(cookie -> cookie.getName().equals(name))
                        .forEach(cookie -> {
                            cookie.setValue("");
                            cookie.setPath("/");
                            cookie.setMaxAge(0);
                            response.addCookie(cookie); // nosemgrep: java.servlets.security.audit.cookie-missing-samesite.cookie-missing-samesite
                            String setCookieHeader = String.format("%s=; Max-Age=0; Path=/; HttpOnly; Secure; SameSite=Lax", name);
                            response.setHeader("Set-Cookie", setCookieHeader); // nosemgrep: java.servlets.security.audit.cookie-missing-samesite.cookie-missing-samesite
                        }));
    }
} 