/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.auth.db.jooq.tables;


import com.chidhagni.auth.db.jooq.DefaultSchema;
import com.chidhagni.auth.db.jooq.Indexes;
import com.chidhagni.auth.db.jooq.Keys;
import com.chidhagni.auth.db.jooq.tables.records.UserPasswordResetRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserPasswordReset extends TableImpl<UserPasswordResetRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>user_password_reset</code>
     */
    public static final UserPasswordReset USER_PASSWORD_RESET = new UserPasswordReset();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<UserPasswordResetRecord> getRecordType() {
        return UserPasswordResetRecord.class;
    }

    /**
     * The column <code>user_password_reset.id</code>.
     */
    public final TableField<UserPasswordResetRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false).defaultValue(DSL.field(DSL.raw("gen_random_uuid()"), SQLDataType.UUID)), this, "");

    /**
     * The column <code>user_password_reset.user_id</code>.
     */
    public final TableField<UserPasswordResetRecord, UUID> USER_ID = createField(DSL.name("user_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>user_password_reset.reset_token</code>.
     */
    public final TableField<UserPasswordResetRecord, String> RESET_TOKEN = createField(DSL.name("reset_token"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>user_password_reset.expires_at</code>.
     */
    public final TableField<UserPasswordResetRecord, LocalDateTime> EXPIRES_AT = createField(DSL.name("expires_at"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>user_password_reset.used</code>.
     */
    public final TableField<UserPasswordResetRecord, Boolean> USED = createField(DSL.name("used"), SQLDataType.BOOLEAN.defaultValue(DSL.field(DSL.raw("false"), SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>user_password_reset.created_at</code>.
     */
    public final TableField<UserPasswordResetRecord, LocalDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.LOCALDATETIME(6).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>user_password_reset.used_at</code>.
     */
    public final TableField<UserPasswordResetRecord, LocalDateTime> USED_AT = createField(DSL.name("used_at"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>user_password_reset.ip_address</code>.
     */
    public final TableField<UserPasswordResetRecord, String> IP_ADDRESS = createField(DSL.name("ip_address"), SQLDataType.VARCHAR(15), this, "");

    /**
     * The column <code>user_password_reset.device_details</code>.
     */
    public final TableField<UserPasswordResetRecord, String> DEVICE_DETAILS = createField(DSL.name("device_details"), SQLDataType.CLOB, this, "");

    private UserPasswordReset(Name alias, Table<UserPasswordResetRecord> aliased) {
        this(alias, aliased, null);
    }

    private UserPasswordReset(Name alias, Table<UserPasswordResetRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>user_password_reset</code> table reference
     */
    public UserPasswordReset(String alias) {
        this(DSL.name(alias), USER_PASSWORD_RESET);
    }

    /**
     * Create an aliased <code>user_password_reset</code> table reference
     */
    public UserPasswordReset(Name alias) {
        this(alias, USER_PASSWORD_RESET);
    }

    /**
     * Create a <code>user_password_reset</code> table reference
     */
    public UserPasswordReset() {
        this(DSL.name("user_password_reset"), null);
    }

    public <O extends Record> UserPasswordReset(Table<O> child, ForeignKey<O, UserPasswordResetRecord> key) {
        super(child, key, USER_PASSWORD_RESET);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.asList(Indexes.IDX_PASSWORD_RESET_ACTIVE, Indexes.IDX_PASSWORD_RESET_EXPIRES_AT);
    }

    @Override
    public UniqueKey<UserPasswordResetRecord> getPrimaryKey() {
        return Keys.USER_PASSWORD_RESET_PKEY;
    }

    @Override
    public List<UniqueKey<UserPasswordResetRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.USER_PASSWORD_RESET_RESET_TOKEN_KEY);
    }

    @Override
    public List<ForeignKey<UserPasswordResetRecord, ?>> getReferences() {
        return Arrays.asList(Keys.USER_PASSWORD_RESET__FK_PASSWORD_RESET_USER);
    }

    private transient Users _users;

    /**
     * Get the implicit join path to the <code>public.users</code> table.
     */
    public Users users() {
        if (_users == null)
            _users = new Users(this, Keys.USER_PASSWORD_RESET__FK_PASSWORD_RESET_USER);

        return _users;
    }

    @Override
    public UserPasswordReset as(String alias) {
        return new UserPasswordReset(DSL.name(alias), this);
    }

    @Override
    public UserPasswordReset as(Name alias) {
        return new UserPasswordReset(alias, this);
    }

    @Override
    public UserPasswordReset as(Table<?> alias) {
        return new UserPasswordReset(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public UserPasswordReset rename(String name) {
        return new UserPasswordReset(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public UserPasswordReset rename(Name name) {
        return new UserPasswordReset(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public UserPasswordReset rename(Table<?> name) {
        return new UserPasswordReset(name.getQualifiedName(), null);
    }
}
