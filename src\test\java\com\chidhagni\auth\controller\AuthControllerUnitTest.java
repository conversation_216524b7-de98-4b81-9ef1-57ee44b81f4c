package com.chidhagni.auth.controller;

import com.chidhagni.auth.dto.*;
import com.chidhagni.auth.service.AuthService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.junit.jupiter.api.Tag;
import org.mockito.ArgumentCaptor;

import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.junit.jupiter.api.Assertions.assertEquals;

@org.junit.jupiter.api.extension.ExtendWith(MockitoExtension.class)
@Tag("unit")
class AuthControllerUnitTest {

    @Mock
    private AuthService authService;

    @InjectMocks
    private AuthController authController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(authController).build();
        objectMapper = new ObjectMapper();
    }

    /**
     * Should register a new user and return success message.
     */
    @Test
    void register_newUser_shouldReturnSuccessMessage() throws Exception {
        RegisterRequest request = RegisterRequestBuilder.builder()
            .email("<EMAIL>")
            .name("Test User")
            .mobileNumber("1234567890")
            .build();
        RegisterResponse response = new RegisterResponse("User registered successfully");
        when(authService.register(any(RegisterRequest.class))).thenReturn(response);
        mockMvc.perform(post("/api/v1/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("User registered successfully"));
        // ArgumentCaptor to verify correct request passed to service
        ArgumentCaptor<RegisterRequest> captor = ArgumentCaptor.forClass(RegisterRequest.class);
        verify(authService).register(captor.capture());
        assertEquals("<EMAIL>", captor.getValue().getEmail());
    }

    /**
     * Should login a user and return session token.
     */
    @Test
    void login_validCredentials_shouldReturnSessionToken() throws Exception {
        LoginRequest request = LoginRequestBuilder.builder()
            .email("<EMAIL>")
            .password("password123")
            .ipAddress("127.0.0.1")
            .deviceDetails("Test Device")
            .overrideExistingLogins(false)
            .build();
        LoginResponse response = new LoginResponse("user-id", "session-token", "refresh-token", "2023-12-31T23:59:59Z", "2024-01-31T23:59:59Z");
        when(authService.login(any(LoginRequest.class))).thenReturn(response);
        mockMvc.perform(post("/api/v1/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.sessionToken").value("session-token"));
        ArgumentCaptor<LoginRequest> captor = ArgumentCaptor.forClass(LoginRequest.class);
        verify(authService).login(captor.capture());
        assertEquals("<EMAIL>", captor.getValue().getEmail());
    }

    @Test
    void verifyEmail_Success() throws Exception {
        String token = "verification-token";
        VerifyEmailResponse response = new VerifyEmailResponse("Email verified successfully", "<EMAIL>", "Test User", "1234567890");
        when(authService.verifyEmail(token)).thenReturn(response);

        mockMvc.perform(get("/api/v1/verify-email")
                .param("token", token))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Email verified successfully"));
    }

    @Test
    void forgotPassword_Success() throws Exception {
        ForgotPasswordRequest request = new ForgotPasswordRequest();
        request.setEmail("<EMAIL>");
        request.setIpAddress("127.0.0.1");
        request.setDeviceDetails("Test Device");
        GenericMessageResponse response = new GenericMessageResponse("Password reset email sent");
        when(authService.forgotPassword(any(ForgotPasswordRequest.class))).thenReturn(response);

        mockMvc.perform(post("/api/v1/forgot-password")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Password reset email sent"));
    }

    @Test
    void resetPassword_Success() throws Exception {
        ResetPasswordRequest request = new ResetPasswordRequest();
        request.setResetToken("reset-token");
        request.setNewPassword("newpassword123");
        request.setIpAddress("127.0.0.1");
        request.setDeviceDetails("Test Device");
        GenericMessageResponse response = new GenericMessageResponse("Password reset successfully");
        when(authService.resetPassword(any(ResetPasswordRequest.class))).thenReturn(response);

        mockMvc.perform(post("/api/v1/reset-password")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Password reset successfully"));
    }

    @Test
    void logout_Success() throws Exception {
        LogoutRequest request = new LogoutRequest();
        request.setSessionToken("session-token");
        request.setIpAddress("127.0.0.1");
        request.setDeviceDetails("Test Device");
        GenericMessageResponse response = new GenericMessageResponse("Logged out successfully");
        when(authService.logout(any(LogoutRequest.class))).thenReturn(response);

        mockMvc.perform(post("/api/v1/logout")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Logged out successfully"));
    }

    @Test
    void resendVerificationLink_Success() throws Exception {
        ResendVerificationLinkRequest request = new ResendVerificationLinkRequest();
        request.setEmail("<EMAIL>");
        GenericMessageResponse response = new GenericMessageResponse("Verification email sent");
        when(authService.resendVerificationLink(any(ResendVerificationLinkRequest.class))).thenReturn(response);

        mockMvc.perform(post("/api/v1/resend-verification-link")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Verification email sent"));
    }

    @Test
    void getUserById_Success() throws Exception {
        UUID userId = UUID.randomUUID();
        UserResponse response = new UserResponse();
        response.setId(userId);
        response.setEmail("<EMAIL>");
        response.setName("Test User");
        when(authService.getUserById(userId)).thenReturn(response);

        mockMvc.perform(get("/api/v1/user/{userId}", userId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.email").value("<EMAIL>"));
    }

    // Add RegisterRequestBuilder and LoginRequestBuilder for test data creation
    static class RegisterRequestBuilder {
        private final RegisterRequest request = new RegisterRequest();
        private RegisterRequestBuilder() {}
        public static RegisterRequestBuilder builder() { return new RegisterRequestBuilder(); }
        public RegisterRequestBuilder email(String email) { request.setEmail(email); return this; }
        public RegisterRequestBuilder name(String name) { request.setName(name); return this; }
        public RegisterRequestBuilder mobileNumber(String mobile) { request.setMobileNumber(mobile); return this; }
        public RegisterRequest build() { return request; }
    }
    static class LoginRequestBuilder {
        private final LoginRequest request = new LoginRequest();
        private LoginRequestBuilder() {}
        public static LoginRequestBuilder builder() { return new LoginRequestBuilder(); }
        public LoginRequestBuilder email(String email) { request.setEmail(email); return this; }
        public LoginRequestBuilder password(String pwd) { request.setPassword(pwd); return this; }
        public LoginRequestBuilder ipAddress(String ip) { request.setIpAddress(ip); return this; }
        public LoginRequestBuilder deviceDetails(String device) { request.setDeviceDetails(device); return this; }
        public LoginRequestBuilder overrideExistingLogins(boolean val) { request.setOverrideExistingLogins(val); return this; }
        public LoginRequest build() { return request; }
    }
} 