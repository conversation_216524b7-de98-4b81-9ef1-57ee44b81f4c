package com.chidhagni.auth;

import com.chidhagni.auth.config.IntegrationTestConfig;
import org.junit.jupiter.api.BeforeAll;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

@Testcontainers
@SpringBootTest
@ActiveProfiles("test")
@Import(IntegrationTestConfig.class)
public abstract class BaseIntegrationTest {

    @Container
    public static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15.3")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test")
            .withStartupTimeout(java.time.Duration.ofMinutes(5))
            .withConnectTimeoutSeconds(60)
            .withReuse(false)  // Disable reuse in CI to avoid container lifecycle issues
            .withInitScript("init-test-db.sql")
            .withCommand("postgres", "-c", "fsync=off", "-c", "synchronous_commit=off", "-c", "max_connections=200");

    @BeforeAll
    static void beforeAll() {
        System.out.println("=== BaseIntegrationTest: Starting PostgreSQL container ===");
        if (!postgres.isRunning()) {
            postgres.start();
        }
        System.out.println("=== BaseIntegrationTest: PostgreSQL container started ===");
        System.out.println("Container JDBC URL: " + postgres.getJdbcUrl());
        System.out.println("Container Username: " + postgres.getUsername());
        System.out.println("Container Database: " + postgres.getDatabaseName());
    }

    @DynamicPropertySource
    static void overrideProps(DynamicPropertyRegistry registry) {
        System.out.println("=== BaseIntegrationTest: Configuring dynamic properties ===");
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        // Enable Liquibase to create database schema during tests
        registry.add("spring.liquibase.enabled", () -> true);
        registry.add("spring.liquibase.change-log", () -> "classpath:db/changelog/db.changelog-master.yaml");
        System.out.println("=== BaseIntegrationTest: Dynamic properties configured ===");
    }
} 