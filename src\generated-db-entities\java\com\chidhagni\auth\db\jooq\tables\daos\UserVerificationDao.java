/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.auth.db.jooq.tables.daos;


import com.chidhagni.auth.db.jooq.tables.UserVerification;
import com.chidhagni.auth.db.jooq.tables.records.UserVerificationRecord;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class UserVerificationDao extends DAOImpl<UserVerificationRecord, com.chidhagni.auth.db.jooq.tables.pojos.UserVerification, UUID> {

    /**
     * Create a new UserVerificationDao without any configuration
     */
    public UserVerificationDao() {
        super(UserVerification.USER_VERIFICATION, com.chidhagni.auth.db.jooq.tables.pojos.UserVerification.class);
    }

    /**
     * Create a new UserVerificationDao with an attached configuration
     */
    @Autowired
    public UserVerificationDao(Configuration configuration) {
        super(UserVerification.USER_VERIFICATION, com.chidhagni.auth.db.jooq.tables.pojos.UserVerification.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.auth.db.jooq.tables.pojos.UserVerification object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(UserVerification.USER_VERIFICATION.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchById(UUID... values) {
        return fetch(UserVerification.USER_VERIFICATION.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.auth.db.jooq.tables.pojos.UserVerification fetchOneById(UUID value) {
        return fetchOne(UserVerification.USER_VERIFICATION.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchOptionalById(UUID value) {
        return fetchOptional(UserVerification.USER_VERIFICATION.ID, value);
    }

    /**
     * Fetch records that have <code>contact_value BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchRangeOfContactValue(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserVerification.USER_VERIFICATION.CONTACT_VALUE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>contact_value IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchByContactValue(String... values) {
        return fetch(UserVerification.USER_VERIFICATION.CONTACT_VALUE, values);
    }

    /**
     * Fetch a unique record that has <code>contact_value = value</code>
     */
    public com.chidhagni.auth.db.jooq.tables.pojos.UserVerification fetchOneByContactValue(String value) {
        return fetchOne(UserVerification.USER_VERIFICATION.CONTACT_VALUE, value);
    }

    /**
     * Fetch a unique record that has <code>contact_value = value</code>
     */
    public Optional<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchOptionalByContactValue(String value) {
        return fetchOptional(UserVerification.USER_VERIFICATION.CONTACT_VALUE, value);
    }

    /**
     * Fetch records that have <code>verification_token BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchRangeOfVerificationToken(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserVerification.USER_VERIFICATION.VERIFICATION_TOKEN, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>verification_token IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchByVerificationToken(String... values) {
        return fetch(UserVerification.USER_VERIFICATION.VERIFICATION_TOKEN, values);
    }

    /**
     * Fetch a unique record that has <code>verification_token = value</code>
     */
    public com.chidhagni.auth.db.jooq.tables.pojos.UserVerification fetchOneByVerificationToken(String value) {
        return fetchOne(UserVerification.USER_VERIFICATION.VERIFICATION_TOKEN, value);
    }

    /**
     * Fetch a unique record that has <code>verification_token = value</code>
     */
    public Optional<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchOptionalByVerificationToken(String value) {
        return fetchOptional(UserVerification.USER_VERIFICATION.VERIFICATION_TOKEN, value);
    }

    /**
     * Fetch records that have <code>expires_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchRangeOfExpiresAt(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(UserVerification.USER_VERIFICATION.EXPIRES_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>expires_at IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchByExpiresAt(LocalDateTime... values) {
        return fetch(UserVerification.USER_VERIFICATION.EXPIRES_AT, values);
    }

    /**
     * Fetch records that have <code>verified BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchRangeOfVerified(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(UserVerification.USER_VERIFICATION.VERIFIED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>verified IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchByVerified(Boolean... values) {
        return fetch(UserVerification.USER_VERIFICATION.VERIFIED, values);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchRangeOfCreatedAt(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(UserVerification.USER_VERIFICATION.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchByCreatedAt(LocalDateTime... values) {
        return fetch(UserVerification.USER_VERIFICATION.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>verified_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchRangeOfVerifiedAt(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(UserVerification.USER_VERIFICATION.VERIFIED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>verified_at IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchByVerifiedAt(LocalDateTime... values) {
        return fetch(UserVerification.USER_VERIFICATION.VERIFIED_AT, values);
    }

    /**
     * Fetch records that have <code>ip_address BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchRangeOfIpAddress(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserVerification.USER_VERIFICATION.IP_ADDRESS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ip_address IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchByIpAddress(String... values) {
        return fetch(UserVerification.USER_VERIFICATION.IP_ADDRESS, values);
    }

    /**
     * Fetch records that have <code>device_details BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchRangeOfDeviceDetails(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserVerification.USER_VERIFICATION.DEVICE_DETAILS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>device_details IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchByDeviceDetails(String... values) {
        return fetch(UserVerification.USER_VERIFICATION.DEVICE_DETAILS, values);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserVerification.USER_VERIFICATION.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchByName(String... values) {
        return fetch(UserVerification.USER_VERIFICATION.NAME, values);
    }

    /**
     * Fetch records that have <code>mobile_number BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchRangeOfMobileNumber(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserVerification.USER_VERIFICATION.MOBILE_NUMBER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mobile_number IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserVerification> fetchByMobileNumber(String... values) {
        return fetch(UserVerification.USER_VERIFICATION.MOBILE_NUMBER, values);
    }
}
