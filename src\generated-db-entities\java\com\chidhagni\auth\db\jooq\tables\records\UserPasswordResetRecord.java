/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.auth.db.jooq.tables.records;


import com.chidhagni.auth.db.jooq.tables.UserPasswordReset;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserPasswordResetRecord extends UpdatableRecordImpl<UserPasswordResetRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>user_password_reset.id</code>.
     */
    public UserPasswordResetRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>user_password_reset.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>user_password_reset.user_id</code>.
     */
    public UserPasswordResetRecord setUserId(UUID value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>user_password_reset.user_id</code>.
     */
    public UUID getUserId() {
        return (UUID) get(1);
    }

    /**
     * Setter for <code>user_password_reset.reset_token</code>.
     */
    public UserPasswordResetRecord setResetToken(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>user_password_reset.reset_token</code>.
     */
    public String getResetToken() {
        return (String) get(2);
    }

    /**
     * Setter for <code>user_password_reset.expires_at</code>.
     */
    public UserPasswordResetRecord setExpiresAt(LocalDateTime value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>user_password_reset.expires_at</code>.
     */
    public LocalDateTime getExpiresAt() {
        return (LocalDateTime) get(3);
    }

    /**
     * Setter for <code>user_password_reset.used</code>.
     */
    public UserPasswordResetRecord setUsed(Boolean value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>user_password_reset.used</code>.
     */
    public Boolean getUsed() {
        return (Boolean) get(4);
    }

    /**
     * Setter for <code>user_password_reset.created_at</code>.
     */
    public UserPasswordResetRecord setCreatedAt(LocalDateTime value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>user_password_reset.created_at</code>.
     */
    public LocalDateTime getCreatedAt() {
        return (LocalDateTime) get(5);
    }

    /**
     * Setter for <code>user_password_reset.used_at</code>.
     */
    public UserPasswordResetRecord setUsedAt(LocalDateTime value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>user_password_reset.used_at</code>.
     */
    public LocalDateTime getUsedAt() {
        return (LocalDateTime) get(6);
    }

    /**
     * Setter for <code>user_password_reset.ip_address</code>.
     */
    public UserPasswordResetRecord setIpAddress(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>user_password_reset.ip_address</code>.
     */
    public String getIpAddress() {
        return (String) get(7);
    }

    /**
     * Setter for <code>user_password_reset.device_details</code>.
     */
    public UserPasswordResetRecord setDeviceDetails(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>user_password_reset.device_details</code>.
     */
    public String getDeviceDetails() {
        return (String) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached UserPasswordResetRecord
     */
    public UserPasswordResetRecord() {
        super(UserPasswordReset.USER_PASSWORD_RESET);
    }

    /**
     * Create a detached, initialised UserPasswordResetRecord
     */
    public UserPasswordResetRecord(UUID id, UUID userId, String resetToken, LocalDateTime expiresAt, Boolean used, LocalDateTime createdAt, LocalDateTime usedAt, String ipAddress, String deviceDetails) {
        super(UserPasswordReset.USER_PASSWORD_RESET);

        setId(id);
        setUserId(userId);
        setResetToken(resetToken);
        setExpiresAt(expiresAt);
        setUsed(used);
        setCreatedAt(createdAt);
        setUsedAt(usedAt);
        setIpAddress(ipAddress);
        setDeviceDetails(deviceDetails);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised UserPasswordResetRecord
     */
    public UserPasswordResetRecord(com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset value) {
        super(UserPasswordReset.USER_PASSWORD_RESET);

        if (value != null) {
            setId(value.getId());
            setUserId(value.getUserId());
            setResetToken(value.getResetToken());
            setExpiresAt(value.getExpiresAt());
            setUsed(value.getUsed());
            setCreatedAt(value.getCreatedAt());
            setUsedAt(value.getUsedAt());
            setIpAddress(value.getIpAddress());
            setDeviceDetails(value.getDeviceDetails());
            resetChangedOnNotNull();
        }
    }
}
