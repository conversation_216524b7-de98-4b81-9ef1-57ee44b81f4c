/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.auth.db.jooq;


import com.chidhagni.auth.db.jooq.tables.UserPasswordReset;
import com.chidhagni.auth.db.jooq.tables.UserSessions;
import com.chidhagni.auth.db.jooq.tables.UserVerification;
import com.chidhagni.auth.db.jooq.tables.Users;
import com.chidhagni.auth.db.jooq.tables.records.UserPasswordResetRecord;
import com.chidhagni.auth.db.jooq.tables.records.UserSessionsRecord;
import com.chidhagni.auth.db.jooq.tables.records.UserVerificationRecord;
import com.chidhagni.auth.db.jooq.tables.records.UsersRecord;

import org.jooq.ForeignKey;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;


/**
 * A class modelling foreign key relationships and constraints of tables in the
 * default schema.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class Keys {

    // -------------------------------------------------------------------------
    // UNIQUE and PRIMARY KEY definitions
    // -------------------------------------------------------------------------

    public static final UniqueKey<UserPasswordResetRecord> USER_PASSWORD_RESET_PKEY = Internal.createUniqueKey(UserPasswordReset.USER_PASSWORD_RESET, DSL.name("user_password_reset_pkey"), new TableField[] { UserPasswordReset.USER_PASSWORD_RESET.ID }, true);
    public static final UniqueKey<UserPasswordResetRecord> USER_PASSWORD_RESET_RESET_TOKEN_KEY = Internal.createUniqueKey(UserPasswordReset.USER_PASSWORD_RESET, DSL.name("user_password_reset_reset_token_key"), new TableField[] { UserPasswordReset.USER_PASSWORD_RESET.RESET_TOKEN }, true);
    public static final UniqueKey<UserSessionsRecord> USER_SESSIONS_PKEY = Internal.createUniqueKey(UserSessions.USER_SESSIONS, DSL.name("user_sessions_pkey"), new TableField[] { UserSessions.USER_SESSIONS.ID }, true);
    public static final UniqueKey<UserSessionsRecord> USER_SESSIONS_REFRESH_TOKEN_KEY = Internal.createUniqueKey(UserSessions.USER_SESSIONS, DSL.name("user_sessions_refresh_token_key"), new TableField[] { UserSessions.USER_SESSIONS.REFRESH_TOKEN }, true);
    public static final UniqueKey<UserSessionsRecord> USER_SESSIONS_SESSION_TOKEN_KEY = Internal.createUniqueKey(UserSessions.USER_SESSIONS, DSL.name("user_sessions_session_token_key"), new TableField[] { UserSessions.USER_SESSIONS.SESSION_TOKEN }, true);
    public static final UniqueKey<UserVerificationRecord> USER_VERIFICATION_CONTACT_VALUE_KEY = Internal.createUniqueKey(UserVerification.USER_VERIFICATION, DSL.name("user_verification_contact_value_key"), new TableField[] { UserVerification.USER_VERIFICATION.CONTACT_VALUE }, true);
    public static final UniqueKey<UserVerificationRecord> USER_VERIFICATION_PKEY = Internal.createUniqueKey(UserVerification.USER_VERIFICATION, DSL.name("user_verification_pkey"), new TableField[] { UserVerification.USER_VERIFICATION.ID }, true);
    public static final UniqueKey<UserVerificationRecord> USER_VERIFICATION_VERIFICATION_TOKEN_KEY = Internal.createUniqueKey(UserVerification.USER_VERIFICATION, DSL.name("user_verification_verification_token_key"), new TableField[] { UserVerification.USER_VERIFICATION.VERIFICATION_TOKEN }, true);
    public static final UniqueKey<UsersRecord> USERS_EMAIL_KEY = Internal.createUniqueKey(Users.USERS, DSL.name("users_email_key"), new TableField[] { Users.USERS.EMAIL }, true);
    public static final UniqueKey<UsersRecord> USERS_PKEY = Internal.createUniqueKey(Users.USERS, DSL.name("users_pkey"), new TableField[] { Users.USERS.ID }, true);

    // -------------------------------------------------------------------------
    // FOREIGN KEY definitions
    // -------------------------------------------------------------------------

    public static final ForeignKey<UserPasswordResetRecord, UsersRecord> USER_PASSWORD_RESET__FK_PASSWORD_RESET_USER = Internal.createForeignKey(UserPasswordReset.USER_PASSWORD_RESET, DSL.name("fk_password_reset_user"), new TableField[] { UserPasswordReset.USER_PASSWORD_RESET.USER_ID }, Keys.USERS_PKEY, new TableField[] { Users.USERS.ID }, true);
    public static final ForeignKey<UserSessionsRecord, UsersRecord> USER_SESSIONS__FK_SESSIONS_USER = Internal.createForeignKey(UserSessions.USER_SESSIONS, DSL.name("fk_sessions_user"), new TableField[] { UserSessions.USER_SESSIONS.USER_ID }, Keys.USERS_PKEY, new TableField[] { Users.USERS.ID }, true);
}
