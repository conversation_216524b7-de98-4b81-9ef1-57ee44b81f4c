name: <PERSON>uild and Push Docker Image

on:
  workflow_call:

jobs:
  docker-build-and-push:
    name: <PERSON>uild and Push Docker Image to DOCR
    runs-on: [self-hosted, linux]
    services:
      docker:
        image: docker:24.0-dind
        options: --privileged
    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'

      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

      - name: Authenticate Docker with DigitalOcean Container Registry (DOCR)
        run: doctl registry login

      - name: Build Docker Image
        run: docker build -t ai-nest-backend .

      - name: Tag Docker Image for DOCR
        run: |
          IMAGE=registry.digitalocean.com/doks-registry/ai-nest-backend:latest
          docker tag ai-nest-backend $IMAGE

      - name: Push Image to DOCR
        run: |
          docker push registry.digitalocean.com/doks-registry/ai-nest-backend:latest
