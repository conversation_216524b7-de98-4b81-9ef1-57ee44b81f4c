/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.auth.db.jooq.tables.pojos;


import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserSessions implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID id;
    private UUID userId;
    private String sessionToken;
    private String refreshToken;
    private LocalDateTime expiresAt;
    private LocalDateTime refreshExpiresAt;
    private Boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime lastAccessedAt;
    private LocalDateTime loggedOutAt;
    private String ipAddress;
    private String deviceDetails;

    public UserSessions() {}

    public UserSessions(UserSessions value) {
        this.id = value.id;
        this.userId = value.userId;
        this.sessionToken = value.sessionToken;
        this.refreshToken = value.refreshToken;
        this.expiresAt = value.expiresAt;
        this.refreshExpiresAt = value.refreshExpiresAt;
        this.isActive = value.isActive;
        this.createdAt = value.createdAt;
        this.lastAccessedAt = value.lastAccessedAt;
        this.loggedOutAt = value.loggedOutAt;
        this.ipAddress = value.ipAddress;
        this.deviceDetails = value.deviceDetails;
    }

    public UserSessions(
        UUID id,
        UUID userId,
        String sessionToken,
        String refreshToken,
        LocalDateTime expiresAt,
        LocalDateTime refreshExpiresAt,
        Boolean isActive,
        LocalDateTime createdAt,
        LocalDateTime lastAccessedAt,
        LocalDateTime loggedOutAt,
        String ipAddress,
        String deviceDetails
    ) {
        this.id = id;
        this.userId = userId;
        this.sessionToken = sessionToken;
        this.refreshToken = refreshToken;
        this.expiresAt = expiresAt;
        this.refreshExpiresAt = refreshExpiresAt;
        this.isActive = isActive;
        this.createdAt = createdAt;
        this.lastAccessedAt = lastAccessedAt;
        this.loggedOutAt = loggedOutAt;
        this.ipAddress = ipAddress;
        this.deviceDetails = deviceDetails;
    }

    /**
     * Getter for <code>user_sessions.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>user_sessions.id</code>.
     */
    public UserSessions setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>user_sessions.user_id</code>.
     */
    public UUID getUserId() {
        return this.userId;
    }

    /**
     * Setter for <code>user_sessions.user_id</code>.
     */
    public UserSessions setUserId(UUID userId) {
        this.userId = userId;
        return this;
    }

    /**
     * Getter for <code>user_sessions.session_token</code>.
     */
    public String getSessionToken() {
        return this.sessionToken;
    }

    /**
     * Setter for <code>user_sessions.session_token</code>.
     */
    public UserSessions setSessionToken(String sessionToken) {
        this.sessionToken = sessionToken;
        return this;
    }

    /**
     * Getter for <code>user_sessions.refresh_token</code>.
     */
    public String getRefreshToken() {
        return this.refreshToken;
    }

    /**
     * Setter for <code>user_sessions.refresh_token</code>.
     */
    public UserSessions setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
        return this;
    }

    /**
     * Getter for <code>user_sessions.expires_at</code>.
     */
    public LocalDateTime getExpiresAt() {
        return this.expiresAt;
    }

    /**
     * Setter for <code>user_sessions.expires_at</code>.
     */
    public UserSessions setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
        return this;
    }

    /**
     * Getter for <code>user_sessions.refresh_expires_at</code>.
     */
    public LocalDateTime getRefreshExpiresAt() {
        return this.refreshExpiresAt;
    }

    /**
     * Setter for <code>user_sessions.refresh_expires_at</code>.
     */
    public UserSessions setRefreshExpiresAt(LocalDateTime refreshExpiresAt) {
        this.refreshExpiresAt = refreshExpiresAt;
        return this;
    }

    /**
     * Getter for <code>user_sessions.is_active</code>.
     */
    public Boolean getIsActive() {
        return this.isActive;
    }

    /**
     * Setter for <code>user_sessions.is_active</code>.
     */
    public UserSessions setIsActive(Boolean isActive) {
        this.isActive = isActive;
        return this;
    }

    /**
     * Getter for <code>user_sessions.created_at</code>.
     */
    public LocalDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>user_sessions.created_at</code>.
     */
    public UserSessions setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>user_sessions.last_accessed_at</code>.
     */
    public LocalDateTime getLastAccessedAt() {
        return this.lastAccessedAt;
    }

    /**
     * Setter for <code>user_sessions.last_accessed_at</code>.
     */
    public UserSessions setLastAccessedAt(LocalDateTime lastAccessedAt) {
        this.lastAccessedAt = lastAccessedAt;
        return this;
    }

    /**
     * Getter for <code>user_sessions.logged_out_at</code>.
     */
    public LocalDateTime getLoggedOutAt() {
        return this.loggedOutAt;
    }

    /**
     * Setter for <code>user_sessions.logged_out_at</code>.
     */
    public UserSessions setLoggedOutAt(LocalDateTime loggedOutAt) {
        this.loggedOutAt = loggedOutAt;
        return this;
    }

    /**
     * Getter for <code>user_sessions.ip_address</code>.
     */
    public String getIpAddress() {
        return this.ipAddress;
    }

    /**
     * Setter for <code>user_sessions.ip_address</code>.
     */
    public UserSessions setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
        return this;
    }

    /**
     * Getter for <code>user_sessions.device_details</code>.
     */
    public String getDeviceDetails() {
        return this.deviceDetails;
    }

    /**
     * Setter for <code>user_sessions.device_details</code>.
     */
    public UserSessions setDeviceDetails(String deviceDetails) {
        this.deviceDetails = deviceDetails;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final UserSessions other = (UserSessions) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.userId == null) {
            if (other.userId != null)
                return false;
        }
        else if (!this.userId.equals(other.userId))
            return false;
        if (this.sessionToken == null) {
            if (other.sessionToken != null)
                return false;
        }
        else if (!this.sessionToken.equals(other.sessionToken))
            return false;
        if (this.refreshToken == null) {
            if (other.refreshToken != null)
                return false;
        }
        else if (!this.refreshToken.equals(other.refreshToken))
            return false;
        if (this.expiresAt == null) {
            if (other.expiresAt != null)
                return false;
        }
        else if (!this.expiresAt.equals(other.expiresAt))
            return false;
        if (this.refreshExpiresAt == null) {
            if (other.refreshExpiresAt != null)
                return false;
        }
        else if (!this.refreshExpiresAt.equals(other.refreshExpiresAt))
            return false;
        if (this.isActive == null) {
            if (other.isActive != null)
                return false;
        }
        else if (!this.isActive.equals(other.isActive))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.lastAccessedAt == null) {
            if (other.lastAccessedAt != null)
                return false;
        }
        else if (!this.lastAccessedAt.equals(other.lastAccessedAt))
            return false;
        if (this.loggedOutAt == null) {
            if (other.loggedOutAt != null)
                return false;
        }
        else if (!this.loggedOutAt.equals(other.loggedOutAt))
            return false;
        if (this.ipAddress == null) {
            if (other.ipAddress != null)
                return false;
        }
        else if (!this.ipAddress.equals(other.ipAddress))
            return false;
        if (this.deviceDetails == null) {
            if (other.deviceDetails != null)
                return false;
        }
        else if (!this.deviceDetails.equals(other.deviceDetails))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.userId == null) ? 0 : this.userId.hashCode());
        result = prime * result + ((this.sessionToken == null) ? 0 : this.sessionToken.hashCode());
        result = prime * result + ((this.refreshToken == null) ? 0 : this.refreshToken.hashCode());
        result = prime * result + ((this.expiresAt == null) ? 0 : this.expiresAt.hashCode());
        result = prime * result + ((this.refreshExpiresAt == null) ? 0 : this.refreshExpiresAt.hashCode());
        result = prime * result + ((this.isActive == null) ? 0 : this.isActive.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.lastAccessedAt == null) ? 0 : this.lastAccessedAt.hashCode());
        result = prime * result + ((this.loggedOutAt == null) ? 0 : this.loggedOutAt.hashCode());
        result = prime * result + ((this.ipAddress == null) ? 0 : this.ipAddress.hashCode());
        result = prime * result + ((this.deviceDetails == null) ? 0 : this.deviceDetails.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("UserSessions (");

        sb.append(id);
        sb.append(", ").append(userId);
        sb.append(", ").append(sessionToken);
        sb.append(", ").append(refreshToken);
        sb.append(", ").append(expiresAt);
        sb.append(", ").append(refreshExpiresAt);
        sb.append(", ").append(isActive);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(lastAccessedAt);
        sb.append(", ").append(loggedOutAt);
        sb.append(", ").append(ipAddress);
        sb.append(", ").append(deviceDetails);

        sb.append(")");
        return sb.toString();
    }
}
