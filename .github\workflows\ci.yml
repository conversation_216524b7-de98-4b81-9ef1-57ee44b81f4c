name: CI Pipeline

on:
  pull_request:
    branches: [main]

jobs:
  # build-test:
  #   uses: ./.github/workflows/build-test.yml
  #   secrets: inherit

  # sonarqube:
  #   uses: ./.github/workflows/sonarqube.yml
  #   needs: build-test
  #   secrets: inherit

  # semgrep-analysis:
  #   uses: ./.github/workflows/semgrep-analysis.yml
  #   needs: sonarqube
  #   secrets: inherit
  docker-build-and-push:
    uses: ./.github/workflows/docker-build-push.yml
    secrets: inherit

  cd-gitops-deploy:
    uses: ./.github/workflows/gitops-deploy.yml
    needs: docker-build-and-push
    secrets: inherit 