# Test-specific configuration

# Testcontainers configuration
spring.datasource.url=${JDBC_DATABASE_URL:***************************************}
spring.datasource.username=${JDBC_DATABASE_USERNAME:test}
spring.datasource.password=${JDBC_DATABASE_PASSWORD:test}

# Enable Liquibase for tests to create tables
spring.liquibase.enabled=true
spring.liquibase.change-log=classpath:db/changelog/db.changelog-master.yaml

# Disable email sending in tests - use mock configuration
spring.mail.host=localhost
spring.mail.port=1025
spring.mail.username=<EMAIL>
spring.mail.password=test
spring.mail.properties.mail.smtp.auth=false
spring.mail.properties.mail.smtp.starttls.enable=false

# Logging for debugging
logging.level.org.testcontainers=DEBUG
logging.level.com.zaxxer.hikari=DEBUG
logging.level.org.springframework.jdbc=DEBUG
logging.level.liquibase=DEBUG
logging.level.liquibase.changelog=DEBUG
logging.level.org.jooq=DEBUG

# Connection pool settings for tests - optimized for CI stability
spring.datasource.hikari.maximum-pool-size=5
spring.datasource.hikari.minimum-idle=1
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=600000
spring.datasource.hikari.validation-timeout=5000
spring.datasource.hikari.leak-detection-threshold=60000

# Disable email sending completely in tests
spring.mail.test-connection=false 