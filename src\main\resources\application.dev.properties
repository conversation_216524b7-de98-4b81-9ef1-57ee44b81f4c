

# DB Configurations
spring.datasource.url=****************************************
spring.datasource.username=auth_db_user
spring.datasource.password=auth_db_password
spring.datasource.driver-class-name=org.postgresql.Driver

server.hostUrl=https://beta.app.chidhagni.co.in
server.port=8092

#CORS configuration
cors.allowed-origins=http://localhost:3000/,http://localhost:3001/
cors.allowed-methods=GET,POST,PUT,DELETE,PATCH,OPTIONS
cors.allow-credentials=true
cors.max-age=3600


# SMTP Email Configuration
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=fqactehafmzlltzz
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.from=<EMAIL>


#logging.level.liquibase=DEBUG
#logging.level.org.springframework=DEBUG


springdoc.swagger-ui.path=/swagger-ui.html

# Google OAuth2 Configuration
spring.security.oauth2.client.registration.google.clientId=1073981864538-********************************.apps.googleusercontent.com
spring.security.oauth2.client.registration.google.clientSecret=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
spring.security.oauth2.client.registration.google.redirectUri={baseUrl}/oauth2/callback/{registrationId}
spring.security.oauth2.client.registration.google.scope=email, profile
# Authorized Redirect URIs (for validation)
app.oauth2.authorizedRedirectUris=http://localhost:8080/oauth2/callback/google, http://localhost:3000/chidhagni/oauth2/redirect, myandroidapp://oauth2/redirect, myiosapp://oauth2/redirect



# JWT Configuration
jwt.secret=verylongchidhganilocalsecretyouwillnotunderstanddahsdsjalkdjsadnsadsalkdjsalkdhacydsahfdlkfjdslkfjdsfpodsifpdsycxiovlkcxvfdfjdshfiusyfuyrerewjrewfdsadsadsadsadsadasdsadsadsadcxvcxvdf
accessToken.expiry=28800000
refreshToken.expiry=86400000

# Session Configuration for OAuth2
server.servlet.session.cookie.same-site=lax
server.servlet.session.cookie.http-only=true
server.servlet.session.cookie.secure=false
server.servlet.session.timeout=30m

sonar.host.url=http://localhost:9002

#HikariCP Connection Pool Settings
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=2
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.pool-name=HikariCP

management.endpoints.web.exposure.include=health,info,prometheus
management.endpoint.prometheus.enabled=true






