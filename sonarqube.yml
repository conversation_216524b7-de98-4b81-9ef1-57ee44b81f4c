version: "3"
services:
  sonarqube:
    image: sonarqube:lts-community
    read_only: true
    depends_on:
      - sonarqubedb
    environment:
      SONAR_JDBC_URL: ****************************************
      SONAR_JDBC_USERNAME: sonar
      SONAR_JDBC_PASSWORD: sonar
    volumes:
      - sonarqube_data:/opt/sonarqube/data
      - sonarqube_extensions:/opt/sonarqube/extensions
      - sonarqube_logs:/opt/sonarqube/logs
    ports:
      - "9002:9000"
    tmpfs:
      - /tmp
      - /opt/sonarqube/temp
    ulimits:
      memlock:
        soft: -1
        hard: -1

  sonarqubedb:
    image: postgres:12
    read_only: true
    environment:
      POSTGRES_USER: sonar
      POSTGRES_PASSWORD: sonar
    volumes:
      - postgresql_sonarqube:/var/lib/postgresql
      - postgresql_sonarqube_data:/var/lib/postgresql/data
    tmpfs:
      - /tmp
      - /var/run/postgresql
      - /var/lib/postgresql/data/pg_stat_tmp

volumes:
  sonarqube_data:
  sonarqube_extensions:
  sonarqube_logs:
  postgresql_sonarqube:
  postgresql_sonarqube_data: 