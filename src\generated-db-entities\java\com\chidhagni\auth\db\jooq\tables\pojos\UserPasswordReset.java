/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.auth.db.jooq.tables.pojos;


import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserPasswordReset implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID id;
    private UUID userId;
    private String resetToken;
    private LocalDateTime expiresAt;
    private Boolean used;
    private LocalDateTime createdAt;
    private LocalDateTime usedAt;
    private String ipAddress;
    private String deviceDetails;

    public UserPasswordReset() {}

    public UserPasswordReset(UserPasswordReset value) {
        this.id = value.id;
        this.userId = value.userId;
        this.resetToken = value.resetToken;
        this.expiresAt = value.expiresAt;
        this.used = value.used;
        this.createdAt = value.createdAt;
        this.usedAt = value.usedAt;
        this.ipAddress = value.ipAddress;
        this.deviceDetails = value.deviceDetails;
    }

    public UserPasswordReset(
        UUID id,
        UUID userId,
        String resetToken,
        LocalDateTime expiresAt,
        Boolean used,
        LocalDateTime createdAt,
        LocalDateTime usedAt,
        String ipAddress,
        String deviceDetails
    ) {
        this.id = id;
        this.userId = userId;
        this.resetToken = resetToken;
        this.expiresAt = expiresAt;
        this.used = used;
        this.createdAt = createdAt;
        this.usedAt = usedAt;
        this.ipAddress = ipAddress;
        this.deviceDetails = deviceDetails;
    }

    /**
     * Getter for <code>user_password_reset.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>user_password_reset.id</code>.
     */
    public UserPasswordReset setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>user_password_reset.user_id</code>.
     */
    public UUID getUserId() {
        return this.userId;
    }

    /**
     * Setter for <code>user_password_reset.user_id</code>.
     */
    public UserPasswordReset setUserId(UUID userId) {
        this.userId = userId;
        return this;
    }

    /**
     * Getter for <code>user_password_reset.reset_token</code>.
     */
    public String getResetToken() {
        return this.resetToken;
    }

    /**
     * Setter for <code>user_password_reset.reset_token</code>.
     */
    public UserPasswordReset setResetToken(String resetToken) {
        this.resetToken = resetToken;
        return this;
    }

    /**
     * Getter for <code>user_password_reset.expires_at</code>.
     */
    public LocalDateTime getExpiresAt() {
        return this.expiresAt;
    }

    /**
     * Setter for <code>user_password_reset.expires_at</code>.
     */
    public UserPasswordReset setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
        return this;
    }

    /**
     * Getter for <code>user_password_reset.used</code>.
     */
    public Boolean getUsed() {
        return this.used;
    }

    /**
     * Setter for <code>user_password_reset.used</code>.
     */
    public UserPasswordReset setUsed(Boolean used) {
        this.used = used;
        return this;
    }

    /**
     * Getter for <code>user_password_reset.created_at</code>.
     */
    public LocalDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>user_password_reset.created_at</code>.
     */
    public UserPasswordReset setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>user_password_reset.used_at</code>.
     */
    public LocalDateTime getUsedAt() {
        return this.usedAt;
    }

    /**
     * Setter for <code>user_password_reset.used_at</code>.
     */
    public UserPasswordReset setUsedAt(LocalDateTime usedAt) {
        this.usedAt = usedAt;
        return this;
    }

    /**
     * Getter for <code>user_password_reset.ip_address</code>.
     */
    public String getIpAddress() {
        return this.ipAddress;
    }

    /**
     * Setter for <code>user_password_reset.ip_address</code>.
     */
    public UserPasswordReset setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
        return this;
    }

    /**
     * Getter for <code>user_password_reset.device_details</code>.
     */
    public String getDeviceDetails() {
        return this.deviceDetails;
    }

    /**
     * Setter for <code>user_password_reset.device_details</code>.
     */
    public UserPasswordReset setDeviceDetails(String deviceDetails) {
        this.deviceDetails = deviceDetails;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final UserPasswordReset other = (UserPasswordReset) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.userId == null) {
            if (other.userId != null)
                return false;
        }
        else if (!this.userId.equals(other.userId))
            return false;
        if (this.resetToken == null) {
            if (other.resetToken != null)
                return false;
        }
        else if (!this.resetToken.equals(other.resetToken))
            return false;
        if (this.expiresAt == null) {
            if (other.expiresAt != null)
                return false;
        }
        else if (!this.expiresAt.equals(other.expiresAt))
            return false;
        if (this.used == null) {
            if (other.used != null)
                return false;
        }
        else if (!this.used.equals(other.used))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.usedAt == null) {
            if (other.usedAt != null)
                return false;
        }
        else if (!this.usedAt.equals(other.usedAt))
            return false;
        if (this.ipAddress == null) {
            if (other.ipAddress != null)
                return false;
        }
        else if (!this.ipAddress.equals(other.ipAddress))
            return false;
        if (this.deviceDetails == null) {
            if (other.deviceDetails != null)
                return false;
        }
        else if (!this.deviceDetails.equals(other.deviceDetails))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.userId == null) ? 0 : this.userId.hashCode());
        result = prime * result + ((this.resetToken == null) ? 0 : this.resetToken.hashCode());
        result = prime * result + ((this.expiresAt == null) ? 0 : this.expiresAt.hashCode());
        result = prime * result + ((this.used == null) ? 0 : this.used.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.usedAt == null) ? 0 : this.usedAt.hashCode());
        result = prime * result + ((this.ipAddress == null) ? 0 : this.ipAddress.hashCode());
        result = prime * result + ((this.deviceDetails == null) ? 0 : this.deviceDetails.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("UserPasswordReset (");

        sb.append(id);
        sb.append(", ").append(userId);
        sb.append(", ").append(resetToken);
        sb.append(", ").append(expiresAt);
        sb.append(", ").append(used);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(usedAt);
        sb.append(", ").append(ipAddress);
        sb.append(", ").append(deviceDetails);

        sb.append(")");
        return sb.toString();
    }
}
