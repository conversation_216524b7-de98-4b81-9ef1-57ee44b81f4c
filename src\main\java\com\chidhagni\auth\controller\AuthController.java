package com.chidhagni.auth.controller;

import com.chidhagni.auth.constants.AuthConstants;
import com.chidhagni.auth.dto.*;
import com.chidhagni.auth.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;


@RestController
@RequestMapping("/api/v1")
@Tag(name = "Authentication", description = "Authentication and user management endpoints")
@Slf4j
public class AuthController {
    private final AuthService authService;

    @Autowired
    public AuthController(AuthService authService) {
        this.authService = authService;
    }

    @PostMapping(value = "/register")
    @Operation(
        summary = "Register a new user",
        description = "Registers a new user and sends a verification email. The user must verify their email before they can create their account."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Registration successful, verification email sent",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = RegisterResponse.class),
                examples = @ExampleObject(
                    value = "{\"message\": \"Verification link sent to your email\"}"
                )
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid input data",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    value = "{\"status\": 400, \"error\": \"Bad Request\", \"message\": \"Invalid email format\"}"
                )
            )
        ),
        @ApiResponse(
            responseCode = "409",
            description = "Email already registered",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    value = "{\"status\": 409, \"error\": \"Conflict\", \"message\": \"Email already registered\"}"
                )
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    value = "{\"status\": 500, \"error\": \"Internal Server Error\", \"message\": \"Failed to send verification email\"}"
                )
            )
        )
    })
    public RegisterResponse register(
            @Parameter(
                description = "User registration details",
                required = true,
                schema = @Schema(implementation = RegisterRequest.class)
            )
            @Valid @RequestBody RegisterRequest request) {
        log.info("POST /register called for email: {}", request.getEmail());
        return authService.register(request);
    }

    @GetMapping(value = "/verify-email", produces= MediaType.APPLICATION_JSON_VALUE)
    @Operation(
        summary = "Verify user email",
        description = "Verifies a user's email address using the token sent in the verification email."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Email verified successfully",
            content = @Content(
                mediaType = AuthConstants.VERIFY_EMAIL_RESPONSE_MIME,
                schema = @Schema(implementation = VerifyEmailResponse.class),
                examples = @ExampleObject(
                    value = "{\"message\": \"Email verified successfully\", \"email\": \"<EMAIL>\", \"name\": \"John Doe\", \"mobileNumber\": \"**********\"}"
                )
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid or expired token",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    value = "{\"status\": 400, \"error\": \"Bad Request\", \"message\": \"Invalid or expired token\"}"
                )
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Token not found",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    value = "{\"status\": 404, \"error\": \"Not Found\", \"message\": \"Invalid or expired token\"}"
                )
            )
        ),
        @ApiResponse(
            responseCode = "409",
            description = "Email already verified",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    value = "{\"status\": 409, \"error\": \"Conflict\", \"message\": \"Email already verified\"}"
                )
            )
        )
    })
    public VerifyEmailResponse verifyEmail(
            @Parameter(
                description = "Verification token from email",
                required = true,
                example = "abc123def456"
            )
            @RequestParam("token") String token) {
        log.info("GET /verify-email called with token: {}", token);
        return authService.verifyEmail(token);
    }

    @PostMapping(value = "/create-user")
    @Operation(
        summary = "Create user account",
        description = "Creates a user account after email verification. This endpoint should be called after the user verifies their email."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "User created successfully",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = CreateUserResponse.class),
                examples = @ExampleObject(
                    value = "{\"userId\": \"123e4567-e89b-12d3-a456-************\", \"sessionToken\": \"session_token_here\", \"refreshToken\": \"refresh_token_here\"}"
                )
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Email not verified or invalid input",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    value = "{\"status\": 400, \"error\": \"Bad Request\", \"message\": \"Email not verified\"}"
                )
            )
        ),
        @ApiResponse(
            responseCode = "409",
            description = "User already exists",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    value = "{\"status\": 409, \"error\": \"Conflict\", \"message\": \"User already exists\"}"
                )
            )
        )
    })
    public CreateUserResponse createUser(
            @Parameter(
                description = "User creation details",
                required = true,
                schema = @Schema(implementation = CreateUserRequest.class)
            )
            @Valid @RequestBody CreateUserRequest request) {
        log.info("POST /create-user called for email: {}", request.getEmail());
        return authService.createUser(request);
    }

    @PostMapping(value = "/resend-verification-link")
    @Operation(
        summary = "Resend verification email",
        description = "Resends the verification email to an unverified user."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Verification email resent",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = GenericMessageResponse.class),
                examples = @ExampleObject(
                    value = "{\"message\": \"Verification link resent\"}"
                )
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "No unverified record found",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    value = "{\"status\": 404, \"error\": \"Not Found\", \"message\": \"No unverified record found\"}"
                )
            )
        )
    })
    public GenericMessageResponse resendVerificationLink(
            @Parameter(
                description = "Email to resend verification to",
                required = true,
                schema = @Schema(implementation = ResendVerificationLinkRequest.class)
            )
            @Valid @RequestBody ResendVerificationLinkRequest request) {
        log.info("POST /resend-verification-link called for email: {}", request.getEmail());
        return authService.resendVerificationLink(request);
    }

    @PostMapping(value = "/forgot-password")
    @Operation(
        summary = "Initiate password reset",
        description = "Sends a password reset link to the user's email address."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Password reset email sent",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = GenericMessageResponse.class),
                examples = @ExampleObject(
                    value = "{\"message\": \"Password reset link sent to your email\"}"
                )
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Email not found",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    value = "{\"status\": 404, \"error\": \"Not Found\", \"message\": \"Email not registered\"}"
                )
            )
        )
    })
    public GenericMessageResponse forgotPassword(
            @Parameter(
                description = "Password reset request details",
                required = true,
                schema = @Schema(implementation = ForgotPasswordRequest.class)
            )
            @Valid @RequestBody ForgotPasswordRequest request) {
        log.info("POST /forgot-password called for email: {}", request.getEmail());
        return authService.forgotPassword(request);
    }

    @PostMapping(value = "/reset-password")
    @Operation(
        summary = "Reset user password",
        description = "Resets the user's password using the token from the password reset email."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Password reset successful",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = GenericMessageResponse.class),
                examples = @ExampleObject(
                    value = "{\"message\": \"Password reset successful\"}"
                )
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid or expired token",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    value = "{\"status\": 400, \"error\": \"Bad Request\", \"message\": \"Invalid or expired token\"}"
                )
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Token not found",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    value = "{\"status\": 404, \"error\": \"Not Found\", \"message\": \"Invalid or expired token\"}"
                )
            )
        )
    })
    public GenericMessageResponse resetPassword(
            @Parameter(
                description = "Password reset details",
                required = true,
                schema = @Schema(implementation = ResetPasswordRequest.class)
            )
            @Valid @RequestBody ResetPasswordRequest request) {
        log.info("POST /reset-password called for token: {}", request.getResetToken());
        return authService.resetPassword(request);
    }

    @PostMapping(value = "/login")
    @Operation(
        summary = "User login",
        description = "Authenticates a user and creates a new session. Returns session tokens for API access."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Login successful",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = LoginResponse.class),
                examples = @ExampleObject(
                    value = "{\"userId\": \"123e4567-e89b-12d3-a456-************\", \"sessionToken\": \"session_token_here\", \"refreshToken\": \"refresh_token_here\", \"expiresAt\": \"2024-01-01T12:00:00Z\", \"refreshExpiresAt\": \"2024-01-08T12:00:00Z\"}"
                )
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid credentials",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    value = "{\"status\": 400, \"error\": \"Bad Request\", \"message\": \"Invalid credentials\"}"
                )
            )
        ),
        @ApiResponse(
            responseCode = "401",
            description = "Email not verified",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    value = "{\"status\": 401, \"error\": \"Unauthorized\", \"message\": \"Email not verified\"}"
                )
            )
        ),
        @ApiResponse(
            responseCode = "403",
            description = "Account locked",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    value = "{\"status\": 403, \"error\": \"Forbidden\", \"message\": \"Account is locked\"}"
                )
            )
        )
    })
    public LoginResponse login(
            @Parameter(
                description = "Login credentials",
                required = true,
                schema = @Schema(implementation = LoginRequest.class)
            )
            @Valid @RequestBody LoginRequest request) {
        log.info("POST /login called for email: {}", request.getEmail());
        return authService.login(request);
    }

    @PostMapping(value = "/logout")
    @Operation(
        summary = "User logout",
        description = "Logs out the user and invalidates their session token."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Logout successful",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = GenericMessageResponse.class),
                examples = @ExampleObject(
                    value = "{\"message\": \"Logged out successfully\"}"
                )
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Session not found or already inactive",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    value = "{\"status\": 404, \"error\": \"Not Found\", \"message\": \"Session not found or already inactive\"}"
                )
            )
        )
    })
    public GenericMessageResponse logout(
            @Parameter(
                description = "Logout request details",
                required = true,
                schema = @Schema(implementation = LogoutRequest.class)
            )
            @Valid @RequestBody LogoutRequest request) {
        log.info("POST /logout called for session token: {}", request.getSessionToken());
        return authService.logout(request);
    }

    @GetMapping(value = "/user/{id}")
    @Operation(
        summary = "Get user by ID",
        description = "Retrieves user information by their unique ID."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "User found",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = UserResponse.class),
                examples = @ExampleObject(
                    value = "{\"id\": \"123e4567-e89b-12d3-a456-************\", \"email\": \"<EMAIL>\", \"name\": \"John Doe\", \"mobileNumber\": \"**********\", \"isActive\": true, \"isEmailVerified\": true, \"isAccountLocked\": false, \"createdOn\": \"2024-01-01T12:00:00Z\", \"updatedOn\": \"2024-01-01T12:00:00Z\"}"
                )
            )
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    value = "{\"status\": 404, \"error\": \"Not Found\", \"message\": \"User not found\"}"
                )
            )
        )
    })
    @PreAuthorize("isAuthenticated()")
    public UserResponse getUserById(
            @Parameter(
                description = "User ID (UUID)",
                required = true,
                example = "123e4567-e89b-12d3-a456-************"
            )
            @PathVariable("id") UUID userId) {
        log.info("GET /user/{} called", userId);
        return authService.getUserById(userId);
    }
}