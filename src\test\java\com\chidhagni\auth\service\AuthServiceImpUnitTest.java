package com.chidhagni.auth.service;

import com.chidhagni.auth.db.jooq.tables.daos.UserPasswordResetDao;
import com.chidhagni.auth.db.jooq.tables.daos.UserSessionsDao;
import com.chidhagni.auth.db.jooq.tables.daos.UserVerificationDao;
import com.chidhagni.auth.db.jooq.tables.daos.UsersDao;
import com.chidhagni.auth.security.TokenProvider;
import com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset;
import com.chidhagni.auth.db.jooq.tables.pojos.UserSessions;
import com.chidhagni.auth.db.jooq.tables.pojos.UserVerification;
import com.chidhagni.auth.db.jooq.tables.pojos.Users;
import com.chidhagni.auth.db.jooq.tables.records.UserPasswordResetRecord;
import com.chidhagni.auth.dto.*;
import com.chidhagni.auth.util.EmailUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jooq.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Answers;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.ArgumentCaptor;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.chidhagni.auth.exception.ApiException;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Counter;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(org.mockito.junit.jupiter.MockitoExtension.class)
class AuthServiceImpUnitTest {
    @Mock private UserVerificationDao userVerificationDao;
    @Mock private UsersDao usersDao;
    @Mock private UserSessionsDao userSessionsDao;
    @Mock private UserPasswordResetDao userPasswordResetDao;
    @Mock(answer = Answers.RETURNS_DEEP_STUBS) private DSLContext dsl;
    @Mock private Configuration configuration;
    @Mock private EmailUtil emailUtil;
    @Mock private ObjectMapper objectMapper;
    @Mock private TokenProvider tokenProvider;
    @Mock private MeterRegistry meterRegistry;
    private AuthServiceImpl authService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        Counter mockCounter = mock(Counter.class);
        when(meterRegistry.counter(anyString(), any(String[].class))).thenReturn(mockCounter);
        authService = new AuthServiceImpl(
            userVerificationDao,
            usersDao,
            userSessionsDao,
            userPasswordResetDao,
            dsl,
            emailUtil,
            objectMapper,
            tokenProvider,
            meterRegistry
        );
        
        // Default email mocking to prevent real email sending
        doNothing().when(emailUtil).sendVerificationEmail(anyString(), anyString());
        doNothing().when(emailUtil).sendResetPasswordEmail(anyString(), anyString());
        when(emailUtil.buildVerificationLink(anyString())).thenReturn("http://localhost:8080/verify?token=test-token");
        when(emailUtil.buildResetLink(anyString())).thenReturn("http://localhost:8080/reset?token=test-token");
    }

    /**
     * Should send verification email and persist user verification for a new user registration.
     */
    @Test
    void register_newUser_shouldSendVerificationEmailAndPersistVerification() {
        RegisterRequest request = new RegisterRequest();
        request.setEmail("<EMAIL>");
        request.setName("Test User");
        request.setMobileNumber("**********");
        when(userVerificationDao.fetchOptionalByContactValue(request.getEmail())).thenReturn(Optional.empty());
        RegisterResponse response = authService.register(request);
        assertEquals("Verification link sent", response.getMessage());
        // ArgumentCaptor to verify email sent to correct address
        ArgumentCaptor<String> emailCaptor = ArgumentCaptor.forClass(String.class);
        verify(emailUtil, times(1)).sendVerificationEmail(emailCaptor.capture(), anyString());
        assertEquals("<EMAIL>", emailCaptor.getValue());
    }

    /**
     * Should not send verification email if user is already verified.
     */
    @Test
    void register_alreadyVerifiedUser_shouldNotSendVerificationEmail() {
        RegisterRequest request = new RegisterRequest();
        request.setEmail("<EMAIL>");
        request.setName("Test User");
        request.setMobileNumber("**********");
        UserVerification existing = new UserVerification();
        existing.setVerified(true);
        when(userVerificationDao.fetchOptionalByContactValue(request.getEmail())).thenReturn(Optional.of(existing));
        RegisterResponse response = authService.register(request);
        assertEquals("Email already verified", response.getMessage());
        verify(emailUtil, never()).sendVerificationEmail(anyString(), anyString());
    }

    @Test
    void verifyEmail_Success() {
        String token = "token";
        UserVerification verification = new UserVerification();
        verification.setVerified(false);
        verification.setExpiresAt(LocalDateTime.now(ZoneOffset.UTC).plusMinutes(10));
        when(userVerificationDao.fetchOptionalByVerificationToken(token)).thenReturn(Optional.of(verification));
        VerifyEmailResponse response = authService.verifyEmail(token);
        assertEquals("Email verified successfully", response.getMessage());
        assertTrue(verification.getVerified());
    }

    @Test
    void verifyEmail_TokenExpired_ThrowsException() {
        String token = "token";
        UserVerification verification = new UserVerification();
        verification.setVerified(false);
        verification.setExpiresAt(LocalDateTime.now(ZoneOffset.UTC).minusMinutes(10));
        when(userVerificationDao.fetchOptionalByVerificationToken(token)).thenReturn(Optional.of(verification));
        Exception ex = assertThrows(Exception.class, () -> authService.verifyEmail(token));
        assertTrue(ex.getMessage().contains("Token expired"));
    }

    @Test
    void createUser_Success() {
        CreateUserRequest request = new CreateUserRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("password");
        request.setIpAddress("127.0.0.1");
        request.setDeviceDetails("device");
        UserVerification verification = new UserVerification();
        verification.setVerified(true);
        verification.setName("Test User");
        verification.setMobileNumber("**********");
        when(userVerificationDao.fetchOptionalByContactValue(request.getEmail())).thenReturn(Optional.of(verification));
        when(usersDao.fetchByEmail(request.getEmail())).thenReturn(java.util.Collections.emptyList());
        CreateUserResponse response = authService.createUser(request);
        assertNotNull(response.getUserId());
        assertNotNull(response.getSessionToken());
        assertNotNull(response.getRefreshToken());
    }

    @Test
    void resendVerificationLink_Unverified_SendsEmail() {
        ResendVerificationLinkRequest request = new ResendVerificationLinkRequest();
        request.setEmail("<EMAIL>");
        UserVerification verification = new UserVerification();
        verification.setVerified(false);
        when(userVerificationDao.fetchOptionalByContactValue(request.getEmail())).thenReturn(Optional.of(verification));
        GenericMessageResponse response = authService.resendVerificationLink(request);
        assertEquals("Verification link resent", response.getMessage());
        verify(emailUtil, times(1)).sendVerificationEmail(eq(request.getEmail()), anyString());
    }

    @Test
    void forgotPassword_UserExists_SendsResetEmail() {
        ForgotPasswordRequest request = new ForgotPasswordRequest();
        request.setEmail("<EMAIL>");
        request.setIpAddress("127.0.0.1");
        request.setDeviceDetails("device");
        Users user = new Users();
        user.setId(UUID.randomUUID());
        when(usersDao.fetchByEmail(request.getEmail())).thenReturn(java.util.Collections.singletonList(user));
        
        // Mock DSLContext update chain for forgotPassword
        UpdateSetFirstStep<UserPasswordResetRecord> updateStep = mock(UpdateSetFirstStep.class);
        UpdateSetMoreStep<UserPasswordResetRecord> setStep = mock(UpdateSetMoreStep.class);
        UpdateSetMoreStep<UserPasswordResetRecord> setStep2 = mock(UpdateSetMoreStep.class);
        UpdateConditionStep<UserPasswordResetRecord> whereStep = mock(UpdateConditionStep.class);
        when(dsl.update(eq(com.chidhagni.auth.db.jooq.Tables.USER_PASSWORD_RESET))).thenReturn(updateStep);
        doReturn(setStep).when(updateStep).set(eq(com.chidhagni.auth.db.jooq.Tables.USER_PASSWORD_RESET.USED), any(Boolean.class));
        doReturn(setStep2).when(setStep).set(eq(com.chidhagni.auth.db.jooq.Tables.USER_PASSWORD_RESET.USED_AT), any(LocalDateTime.class));
        doReturn(whereStep).when(setStep2).where(any(Condition.class));
        when(whereStep.execute()).thenReturn(1);
        
        GenericMessageResponse response = authService.forgotPassword(request);
        assertEquals("Password reset link sent to your email", response.getMessage());
        verify(emailUtil, times(1)).sendResetPasswordEmail(eq(request.getEmail()), anyString());
    }

    @Test
    void resetPassword_Success() {
        ResetPasswordRequest request = new ResetPasswordRequest();
        request.setResetToken("token");
        request.setNewPassword("newpassword");
        request.setIpAddress("127.0.0.1");
        request.setDeviceDetails("device");
        UserPasswordReset reset = new UserPasswordReset();
        reset.setUsed(false);
        reset.setExpiresAt(LocalDateTime.now(ZoneOffset.UTC).plusMinutes(10));
        reset.setUserId(UUID.randomUUID());
        when(userPasswordResetDao.fetchOptionalByResetToken(request.getResetToken())).thenReturn(Optional.of(reset));
        Users user = new Users();
        user.setId(reset.getUserId());
        when(usersDao.fetchOptionalById(reset.getUserId())).thenReturn(Optional.of(user));
        GenericMessageResponse response = authService.resetPassword(request);
        assertEquals("Password reset successful", response.getMessage());
    }

    @Test
    void login_Success() {
        LoginRequest request = new LoginRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("password");
        request.setIpAddress("127.0.0.1");
        request.setDeviceDetails("device");
        request.setOverrideExistingLogins(false);
        Users user = new Users();
        user.setId(UUID.randomUUID());
        user.setIsActive(true);
        user.setPassword(new org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder().encode("password"));
        when(usersDao.fetchByEmail(request.getEmail())).thenReturn(java.util.Collections.singletonList(user));
        // Stub update and insert methods to avoid NullPointerException
        doNothing().when(usersDao).update(any(Users.class));
        doNothing().when(userSessionsDao).insert(any(com.chidhagni.auth.db.jooq.tables.pojos.UserSessions.class));
        // Mock TokenProvider methods for this test
        when(tokenProvider.createToken(any(UUID.class))).thenReturn("jwt-session-token");
        when(tokenProvider.createRefreshToken(any(UUID.class))).thenReturn("jwt-refresh-token");
        when(tokenProvider.getTokenExpiry(anyString())).thenReturn("2024-12-31T23:59:59Z");
        LoginResponse response = authService.login(request);
        assertNotNull(response.getUserId());
        assertNotNull(response.getSessionToken());
        assertNotNull(response.getRefreshToken());
    }

    @Test
    void logout_Success() {
        LogoutRequest request = new LogoutRequest();
        request.setSessionToken("token");
        request.setIpAddress("127.0.0.1");
        request.setDeviceDetails("device");
        UserSessions session = new UserSessions();
        session.setIsActive(true);
        when(userSessionsDao.fetchBySessionToken(request.getSessionToken())).thenReturn(java.util.Collections.singletonList(session));
        GenericMessageResponse response = authService.logout(request);
        assertEquals("Logged out successfully", response.getMessage());
    }

    @Test
    void getUserById_Success() {
        UUID userId = UUID.randomUUID();
        Users user = new Users();
        user.setId(userId);
        user.setEmail("<EMAIL>");
        user.setName("Test User");
        user.setMobileNumber("**********");
        user.setIsActive(true);
        user.setEmailVerified(true);
        user.setAccountLocked(false);
        user.setCreatedOn(LocalDateTime.now(ZoneOffset.UTC));
        user.setUpdatedOn(LocalDateTime.now(ZoneOffset.UTC));
        when(usersDao.fetchOptionalById(userId)).thenReturn(Optional.of(user));
        UserResponse response = authService.getUserById(userId);
        assertEquals(userId, response.getId());
        assertEquals("<EMAIL>", response.getEmail());
    }

    @Test
    void testFindUserByEmail_returnsUser() {
        Users user = new Users();
        user.setEmail("<EMAIL>");
        when(usersDao.fetchByEmail("<EMAIL>")).thenReturn(java.util.Collections.singletonList(user));
        Optional<Users> result = authService.findUserByEmail("<EMAIL>");
        assertTrue(result.isPresent());
        assertEquals("<EMAIL>", result.get().getEmail());
    }

    @Test
    void testFindUserByEmail_returnsEmpty() {
        when(usersDao.fetchByEmail("<EMAIL>")).thenReturn(java.util.Collections.emptyList());
        Optional<Users> result = authService.findUserByEmail("<EMAIL>");
        assertFalse(result.isPresent());
    }

    @Test
    void testCreateOAuth2User_setsTimestampsAndInserts() {
        Users user = new Users();
        user.setEmail("<EMAIL>");
        doNothing().when(usersDao).insert(any(Users.class));
        Users result = authService.createOAuth2User(user);
        assertNotNull(result.getCreatedOn());
        assertNotNull(result.getUpdatedOn());
        verify(usersDao, times(1)).insert(user);
    }

    @Test
    void testUpdateOAuth2User_setsUpdatedOnAndUpdates() {
        Users user = new Users();
        user.setEmail("<EMAIL>");
        doNothing().when(usersDao).update(any(Users.class));
        Users result = authService.updateOAuth2User(user);
        assertNotNull(result.getUpdatedOn());
        verify(usersDao, times(1)).update(user);
    }

    @Test
    void testLogin_withOverrideExistingLogins_deactivatesSessions() {
        LoginRequest request = new LoginRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("password");
        request.setIpAddress("127.0.0.1");
        request.setDeviceDetails("device");
        request.setOverrideExistingLogins(true);
        Users user = new Users();
        user.setId(UUID.randomUUID());
        user.setIsActive(true);
        user.setPassword(new org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder().encode("password"));
        when(usersDao.fetchByEmail(request.getEmail())).thenReturn(java.util.Collections.singletonList(user));
        // Mock userSessionsDao.fetchByUserId to return active sessions
        com.chidhagni.auth.db.jooq.tables.pojos.UserSessions session1 = new com.chidhagni.auth.db.jooq.tables.pojos.UserSessions();
        session1.setIsActive(true);
        session1.setUserId(user.getId());
        doNothing().when(userSessionsDao).update(any(com.chidhagni.auth.db.jooq.tables.pojos.UserSessions.class));
        when(userSessionsDao.fetchByUserId(user.getId())).thenReturn(java.util.Collections.singletonList(session1));
        doNothing().when(usersDao).update(any(Users.class));
        doNothing().when(userSessionsDao).insert(any(com.chidhagni.auth.db.jooq.tables.pojos.UserSessions.class));
        LoginResponse response = authService.login(request);
        assertNotNull(response.getUserId());
        verify(userSessionsDao, times(1)).fetchByUserId(user.getId());
        verify(userSessionsDao, atLeastOnce()).update(any(com.chidhagni.auth.db.jooq.tables.pojos.UserSessions.class));
    }

    @Test
    void register_DaoFailure_ThrowsApiException() {
        RegisterRequest request = new RegisterRequest();
        request.setEmail("<EMAIL>");
        request.setName("Test User");
        request.setMobileNumber("**********");
        doThrow(new RuntimeException("DB error")).when(userVerificationDao).fetchOptionalByContactValue(anyString());
        Exception ex = assertThrows(ApiException.class, () -> authService.register(request));
        assertNotNull(ex);
    }

    @Test
    void login_DaoFailure_ThrowsApiException() {
        LoginRequest request = new LoginRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("password");
        request.setIpAddress("127.0.0.1");
        request.setDeviceDetails("device");
        request.setOverrideExistingLogins(false);
        doThrow(new RuntimeException("DB error")).when(usersDao).fetchByEmail(anyString());
        Exception ex = assertThrows(ApiException.class, () -> authService.login(request));
        assertNotNull(ex);
    }

    @Test
    void register_EmailSendFailure_ThrowsApiException() {
        RegisterRequest request = new RegisterRequest();
        request.setEmail("<EMAIL>");
        request.setName("Test User");
        request.setMobileNumber("**********");
        when(userVerificationDao.fetchOptionalByContactValue(request.getEmail())).thenReturn(Optional.empty());
        doThrow(new RuntimeException("Email send error")).when(emailUtil).sendVerificationEmail(anyString(), anyString());
        Exception ex = assertThrows(ApiException.class, () -> authService.register(request));
        assertNotNull(ex);
    }

    @Test
    void forgotPassword_EmailSendFailure_ThrowsApiException() {
        ForgotPasswordRequest request = new ForgotPasswordRequest();
        request.setEmail("<EMAIL>");
        request.setIpAddress("127.0.0.1");
        request.setDeviceDetails("device");
        Users user = new Users();
        user.setId(UUID.randomUUID());
        when(usersDao.fetchByEmail(request.getEmail())).thenReturn(java.util.Collections.singletonList(user));
        doThrow(new RuntimeException("Email send error")).when(emailUtil).sendResetPasswordEmail(anyString(), anyString());
        Exception ex = assertThrows(ApiException.class, () -> authService.forgotPassword(request));
        assertNotNull(ex);
    }

    @Test
    void getAllUsers_shouldReturnUserListResponse() {
        Users user1 = new Users();
        user1.setId(UUID.randomUUID());
        user1.setEmail("<EMAIL>");
        user1.setName("User One");
        user1.setMobileNumber("**********");
        user1.setIsActive(true);
        user1.setEmailVerified(true);
        user1.setAccountLocked(false);
        user1.setCreatedOn(LocalDateTime.now());
        user1.setUpdatedOn(LocalDateTime.now());

        Users user2 = new Users();
        user2.setId(UUID.randomUUID());
        user2.setEmail("<EMAIL>");
        user2.setName("User Two");
        user2.setMobileNumber("**********");
        user2.setIsActive(true);
        user2.setEmailVerified(false);
        user2.setAccountLocked(false);
        user2.setCreatedOn(LocalDateTime.now());
        user2.setUpdatedOn(LocalDateTime.now());

        when(usersDao.findAll()).thenReturn(java.util.List.of(user1, user2));

        AdminUserListResponse response = authService.getAllUsers();
        assertNotNull(response);
        assertEquals(2, response.getUsers().size());
        assertEquals("<EMAIL>", response.getUsers().get(0).getEmail());
        assertEquals("<EMAIL>", response.getUsers().get(1).getEmail());
    }

    // Add UsersBuilder for test data creation
    static class UsersBuilder {
        private final Users user = new Users();
        public UsersBuilder id(UUID id) { user.setId(id); return this; }
        public UsersBuilder email(String email) { user.setEmail(email); return this; }
        public UsersBuilder isActive(boolean active) { user.setIsActive(active); return this; }
        public UsersBuilder emailVerified(boolean verified) { user.setEmailVerified(verified); return this; }
        public UsersBuilder accountLocked(boolean locked) { user.setAccountLocked(locked); return this; }
        public UsersBuilder name(String name) { user.setName(name); return this; }
        public UsersBuilder mobileNumber(String mobile) { user.setMobileNumber(mobile); return this; }
        public Users build() { return user; }
    }
}