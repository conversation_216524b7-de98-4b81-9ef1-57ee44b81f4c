package com.chidhagni.auth.util;

import jakarta.mail.internet.MimeMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.Tag;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.*;

@Tag("unit")
@ExtendWith(MockitoExtension.class)
class EmailUtilUnitTest {
    @Mock
    private JavaMailSender mailSender;
    @Mock
    private MimeMessage mimeMessage;
    @InjectMocks
    private EmailUtil emailUtil;
    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(emailUtil, "fromEmail", "<EMAIL>");
        when(mailSender.createMimeMessage()).thenReturn(mimeMessage);
    }
    @Test
    void sendEmail_Success() {
        String to = "<EMAIL>";
        String subject = "Test Subject";
        String body = "Test Body";
        assertDoesNotThrow(() -> emailUtil.sendEmail(to, subject, body));
        verify(mailSender, times(1)).createMimeMessage();
    }
} 