/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.auth.db.jooq.tables.records;


import com.chidhagni.auth.db.jooq.tables.UserVerification;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserVerificationRecord extends UpdatableRecordImpl<UserVerificationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>user_verification.id</code>.
     */
    public UserVerificationRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>user_verification.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>user_verification.contact_value</code>.
     */
    public UserVerificationRecord setContactValue(String value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>user_verification.contact_value</code>.
     */
    public String getContactValue() {
        return (String) get(1);
    }

    /**
     * Setter for <code>user_verification.verification_token</code>.
     */
    public UserVerificationRecord setVerificationToken(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>user_verification.verification_token</code>.
     */
    public String getVerificationToken() {
        return (String) get(2);
    }

    /**
     * Setter for <code>user_verification.expires_at</code>.
     */
    public UserVerificationRecord setExpiresAt(LocalDateTime value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>user_verification.expires_at</code>.
     */
    public LocalDateTime getExpiresAt() {
        return (LocalDateTime) get(3);
    }

    /**
     * Setter for <code>user_verification.verified</code>.
     */
    public UserVerificationRecord setVerified(Boolean value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>user_verification.verified</code>.
     */
    public Boolean getVerified() {
        return (Boolean) get(4);
    }

    /**
     * Setter for <code>user_verification.created_at</code>.
     */
    public UserVerificationRecord setCreatedAt(LocalDateTime value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>user_verification.created_at</code>.
     */
    public LocalDateTime getCreatedAt() {
        return (LocalDateTime) get(5);
    }

    /**
     * Setter for <code>user_verification.verified_at</code>.
     */
    public UserVerificationRecord setVerifiedAt(LocalDateTime value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>user_verification.verified_at</code>.
     */
    public LocalDateTime getVerifiedAt() {
        return (LocalDateTime) get(6);
    }

    /**
     * Setter for <code>user_verification.ip_address</code>.
     */
    public UserVerificationRecord setIpAddress(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>user_verification.ip_address</code>.
     */
    public String getIpAddress() {
        return (String) get(7);
    }

    /**
     * Setter for <code>user_verification.device_details</code>.
     */
    public UserVerificationRecord setDeviceDetails(String value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>user_verification.device_details</code>.
     */
    public String getDeviceDetails() {
        return (String) get(8);
    }

    /**
     * Setter for <code>user_verification.name</code>.
     */
    public UserVerificationRecord setName(String value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>user_verification.name</code>.
     */
    public String getName() {
        return (String) get(9);
    }

    /**
     * Setter for <code>user_verification.mobile_number</code>.
     */
    public UserVerificationRecord setMobileNumber(String value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>user_verification.mobile_number</code>.
     */
    public String getMobileNumber() {
        return (String) get(10);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached UserVerificationRecord
     */
    public UserVerificationRecord() {
        super(UserVerification.USER_VERIFICATION);
    }

    /**
     * Create a detached, initialised UserVerificationRecord
     */
    public UserVerificationRecord(UUID id, String contactValue, String verificationToken, LocalDateTime expiresAt, Boolean verified, LocalDateTime createdAt, LocalDateTime verifiedAt, String ipAddress, String deviceDetails, String name, String mobileNumber) {
        super(UserVerification.USER_VERIFICATION);

        setId(id);
        setContactValue(contactValue);
        setVerificationToken(verificationToken);
        setExpiresAt(expiresAt);
        setVerified(verified);
        setCreatedAt(createdAt);
        setVerifiedAt(verifiedAt);
        setIpAddress(ipAddress);
        setDeviceDetails(deviceDetails);
        setName(name);
        setMobileNumber(mobileNumber);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised UserVerificationRecord
     */
    public UserVerificationRecord(com.chidhagni.auth.db.jooq.tables.pojos.UserVerification value) {
        super(UserVerification.USER_VERIFICATION);

        if (value != null) {
            setId(value.getId());
            setContactValue(value.getContactValue());
            setVerificationToken(value.getVerificationToken());
            setExpiresAt(value.getExpiresAt());
            setVerified(value.getVerified());
            setCreatedAt(value.getCreatedAt());
            setVerifiedAt(value.getVerifiedAt());
            setIpAddress(value.getIpAddress());
            setDeviceDetails(value.getDeviceDetails());
            setName(value.getName());
            setMobileNumber(value.getMobileNumber());
            resetChangedOnNotNull();
        }
    }
}
