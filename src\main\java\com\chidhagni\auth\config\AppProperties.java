package com.chidhagni.auth.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@ConfigurationProperties(prefix = "app")
public class AppProperties {
    private final OAuth2 oauth2 = new OAuth2();

    public static final class OAuth2 {
        private List<String> authorizedRedirectUris = new ArrayList<>();
        private String defaultTargetUrl = "http://localhost:3000/oauth2/redirect";

        public List<String> getAuthorizedRedirectUris() {
            return authorizedRedirectUris;
        }

        public OAuth2 authorizedRedirectUris(List<String> authorizedRedirectUris) {
            this.authorizedRedirectUris = authorizedRedirectUris;
            return this;
        }

        public String getDefaultTargetUrl() {
            return defaultTargetUrl;
        }

        public OAuth2 defaultTargetUrl(String defaultTargetUrl) {
            this.defaultTargetUrl = defaultTargetUrl;
            return this;
        }
    }

    public OAuth2 getOauth2() {
        return oauth2;
    }
} 