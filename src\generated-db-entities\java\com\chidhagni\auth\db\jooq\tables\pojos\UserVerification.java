/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.auth.db.jooq.tables.pojos;


import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserVerification implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID id;
    private String contactValue;
    private String verificationToken;
    private LocalDateTime expiresAt;
    private Boolean verified;
    private LocalDateTime createdAt;
    private LocalDateTime verifiedAt;
    private String ipAddress;
    private String deviceDetails;
    private String name;
    private String mobileNumber;

    public UserVerification() {}

    public UserVerification(UserVerification value) {
        this.id = value.id;
        this.contactValue = value.contactValue;
        this.verificationToken = value.verificationToken;
        this.expiresAt = value.expiresAt;
        this.verified = value.verified;
        this.createdAt = value.createdAt;
        this.verifiedAt = value.verifiedAt;
        this.ipAddress = value.ipAddress;
        this.deviceDetails = value.deviceDetails;
        this.name = value.name;
        this.mobileNumber = value.mobileNumber;
    }

    public UserVerification(
        UUID id,
        String contactValue,
        String verificationToken,
        LocalDateTime expiresAt,
        Boolean verified,
        LocalDateTime createdAt,
        LocalDateTime verifiedAt,
        String ipAddress,
        String deviceDetails,
        String name,
        String mobileNumber
    ) {
        this.id = id;
        this.contactValue = contactValue;
        this.verificationToken = verificationToken;
        this.expiresAt = expiresAt;
        this.verified = verified;
        this.createdAt = createdAt;
        this.verifiedAt = verifiedAt;
        this.ipAddress = ipAddress;
        this.deviceDetails = deviceDetails;
        this.name = name;
        this.mobileNumber = mobileNumber;
    }

    /**
     * Getter for <code>user_verification.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>user_verification.id</code>.
     */
    public UserVerification setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>user_verification.contact_value</code>.
     */
    public String getContactValue() {
        return this.contactValue;
    }

    /**
     * Setter for <code>user_verification.contact_value</code>.
     */
    public UserVerification setContactValue(String contactValue) {
        this.contactValue = contactValue;
        return this;
    }

    /**
     * Getter for <code>user_verification.verification_token</code>.
     */
    public String getVerificationToken() {
        return this.verificationToken;
    }

    /**
     * Setter for <code>user_verification.verification_token</code>.
     */
    public UserVerification setVerificationToken(String verificationToken) {
        this.verificationToken = verificationToken;
        return this;
    }

    /**
     * Getter for <code>user_verification.expires_at</code>.
     */
    public LocalDateTime getExpiresAt() {
        return this.expiresAt;
    }

    /**
     * Setter for <code>user_verification.expires_at</code>.
     */
    public UserVerification setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
        return this;
    }

    /**
     * Getter for <code>user_verification.verified</code>.
     */
    public Boolean getVerified() {
        return this.verified;
    }

    /**
     * Setter for <code>user_verification.verified</code>.
     */
    public UserVerification setVerified(Boolean verified) {
        this.verified = verified;
        return this;
    }

    /**
     * Getter for <code>user_verification.created_at</code>.
     */
    public LocalDateTime getCreatedAt() {
        return this.createdAt;
    }

    /**
     * Setter for <code>user_verification.created_at</code>.
     */
    public UserVerification setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
        return this;
    }

    /**
     * Getter for <code>user_verification.verified_at</code>.
     */
    public LocalDateTime getVerifiedAt() {
        return this.verifiedAt;
    }

    /**
     * Setter for <code>user_verification.verified_at</code>.
     */
    public UserVerification setVerifiedAt(LocalDateTime verifiedAt) {
        this.verifiedAt = verifiedAt;
        return this;
    }

    /**
     * Getter for <code>user_verification.ip_address</code>.
     */
    public String getIpAddress() {
        return this.ipAddress;
    }

    /**
     * Setter for <code>user_verification.ip_address</code>.
     */
    public UserVerification setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
        return this;
    }

    /**
     * Getter for <code>user_verification.device_details</code>.
     */
    public String getDeviceDetails() {
        return this.deviceDetails;
    }

    /**
     * Setter for <code>user_verification.device_details</code>.
     */
    public UserVerification setDeviceDetails(String deviceDetails) {
        this.deviceDetails = deviceDetails;
        return this;
    }

    /**
     * Getter for <code>user_verification.name</code>.
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>user_verification.name</code>.
     */
    public UserVerification setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>user_verification.mobile_number</code>.
     */
    public String getMobileNumber() {
        return this.mobileNumber;
    }

    /**
     * Setter for <code>user_verification.mobile_number</code>.
     */
    public UserVerification setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final UserVerification other = (UserVerification) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.contactValue == null) {
            if (other.contactValue != null)
                return false;
        }
        else if (!this.contactValue.equals(other.contactValue))
            return false;
        if (this.verificationToken == null) {
            if (other.verificationToken != null)
                return false;
        }
        else if (!this.verificationToken.equals(other.verificationToken))
            return false;
        if (this.expiresAt == null) {
            if (other.expiresAt != null)
                return false;
        }
        else if (!this.expiresAt.equals(other.expiresAt))
            return false;
        if (this.verified == null) {
            if (other.verified != null)
                return false;
        }
        else if (!this.verified.equals(other.verified))
            return false;
        if (this.createdAt == null) {
            if (other.createdAt != null)
                return false;
        }
        else if (!this.createdAt.equals(other.createdAt))
            return false;
        if (this.verifiedAt == null) {
            if (other.verifiedAt != null)
                return false;
        }
        else if (!this.verifiedAt.equals(other.verifiedAt))
            return false;
        if (this.ipAddress == null) {
            if (other.ipAddress != null)
                return false;
        }
        else if (!this.ipAddress.equals(other.ipAddress))
            return false;
        if (this.deviceDetails == null) {
            if (other.deviceDetails != null)
                return false;
        }
        else if (!this.deviceDetails.equals(other.deviceDetails))
            return false;
        if (this.name == null) {
            if (other.name != null)
                return false;
        }
        else if (!this.name.equals(other.name))
            return false;
        if (this.mobileNumber == null) {
            if (other.mobileNumber != null)
                return false;
        }
        else if (!this.mobileNumber.equals(other.mobileNumber))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.contactValue == null) ? 0 : this.contactValue.hashCode());
        result = prime * result + ((this.verificationToken == null) ? 0 : this.verificationToken.hashCode());
        result = prime * result + ((this.expiresAt == null) ? 0 : this.expiresAt.hashCode());
        result = prime * result + ((this.verified == null) ? 0 : this.verified.hashCode());
        result = prime * result + ((this.createdAt == null) ? 0 : this.createdAt.hashCode());
        result = prime * result + ((this.verifiedAt == null) ? 0 : this.verifiedAt.hashCode());
        result = prime * result + ((this.ipAddress == null) ? 0 : this.ipAddress.hashCode());
        result = prime * result + ((this.deviceDetails == null) ? 0 : this.deviceDetails.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.mobileNumber == null) ? 0 : this.mobileNumber.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("UserVerification (");

        sb.append(id);
        sb.append(", ").append(contactValue);
        sb.append(", ").append(verificationToken);
        sb.append(", ").append(expiresAt);
        sb.append(", ").append(verified);
        sb.append(", ").append(createdAt);
        sb.append(", ").append(verifiedAt);
        sb.append(", ").append(ipAddress);
        sb.append(", ").append(deviceDetails);
        sb.append(", ").append(name);
        sb.append(", ").append(mobileNumber);

        sb.append(")");
        return sb.toString();
    }
}
