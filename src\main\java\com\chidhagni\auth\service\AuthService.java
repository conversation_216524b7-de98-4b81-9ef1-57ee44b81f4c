package com.chidhagni.auth.service;

import com.chidhagni.auth.db.jooq.tables.pojos.Users;
import com.chidhagni.auth.dto.*;

import java.util.Optional;
import java.util.UUID;

public interface AuthService {
    /**
     * Register a new user and send verification link.
     */
    RegisterResponse register(RegisterRequest request);

    /**
     * Verify email using token.
     */
    VerifyEmailResponse verifyEmail(String token);

    /**
     * Create user after verification.
     */
    CreateUserResponse createUser(CreateUserRequest request);

    /**
     * Resend verification link if not verified.
     */
    GenericMessageResponse resendVerificationLink(ResendVerificationLinkRequest request);

    /**
     * Send forgot password link.
     */
    GenericMessageResponse forgotPassword(ForgotPasswordRequest request);

    /**
     * Reset password using token.
     */
    GenericMessageResponse resetPassword(ResetPasswordRequest request);

    /**
     * Login and create session.
     */
    LoginResponse login(LoginRequest request);

    /**
     * Logout and deactivate session.
     */
    GenericMessageResponse logout(LogoutRequest request);

    /**
     * Get user by ID.
     */
    UserResponse getUserById(UUID userId);

    /**
     * Get all users (Admin only).
     */
    AdminUserListResponse getAllUsers();

    /**
     * Find user by email.
     */
    Optional<Users> findUserByEmail(String email);

    /**
     * Create OAuth2 user.
     */
    Users createOAuth2User(Users user);

    /**
     * Update OAuth2 user.
     */
    Users updateOAuth2User(Users user);
}