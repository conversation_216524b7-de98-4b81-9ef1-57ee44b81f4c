package com.chidhagni.auth.security.oauth2;

import com.chidhagni.auth.db.jooq.tables.daos.UserVerificationDao;
import com.chidhagni.auth.db.jooq.tables.pojos.Users;
import com.chidhagni.auth.security.UserPrincipal;
import com.chidhagni.auth.security.oauth2.user.OAuth2UserInfo;
import com.chidhagni.auth.security.oauth2.user.OAuth2UserInfoFactory;
import com.chidhagni.auth.service.AuthService;
import com.chidhagni.auth.db.jooq.tables.daos.UserSessionsDao;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.core.user.DefaultOAuth2User;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;

import java.lang.reflect.Method;
import java.lang.reflect.InvocationTargetException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

class CustomOAuth2UserServiceUnitTest {
    @Mock
    private AuthService authService;
    @Mock
    private UserVerificationDao userVerificationDao;
    @Mock
    private UserSessionsDao userSessionsDao;
    private CustomOAuth2UserService service;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        service = new CustomOAuth2UserService();
        // Use reflection to set private fields
        var authServiceField = CustomOAuth2UserService.class.getDeclaredField("authService");
        authServiceField.setAccessible(true);
        authServiceField.set(service, authService);
        var userVerificationDaoField = CustomOAuth2UserService.class.getDeclaredField("userVerificationDao");
        userVerificationDaoField.setAccessible(true);
        userVerificationDaoField.set(service, userVerificationDao);
        var userSessionsDaoField = CustomOAuth2UserService.class.getDeclaredField("userSessionsDao");
        userSessionsDaoField.setAccessible(true);
        userSessionsDaoField.set(service, userSessionsDao);
        doNothing().when(userSessionsDao).insert(any(com.chidhagni.auth.db.jooq.tables.pojos.UserSessions.class));
    }

    @Test
    void testProcessOAuth2User_registersNewUser() throws Exception {
        OAuth2UserRequest userRequest = mock(OAuth2UserRequest.class);
        ClientRegistration clientRegistration = mock(ClientRegistration.class);
        when(userRequest.getClientRegistration()).thenReturn(clientRegistration);
        when(clientRegistration.getRegistrationId()).thenReturn("google");
        Map<String, Object> attributes = new HashMap<>();
        attributes.put("email", "<EMAIL>");
        attributes.put("name", "Test User");
        attributes.put("sub", "123");
        attributes.put("picture", "http://img");
        OAuth2User oAuth2User = new DefaultOAuth2User(Collections.emptyList(), attributes, "sub");
        when(authService.findUserByEmail(anyString())).thenReturn(Optional.empty());
        Users user = new Users();
        user.setEmail("<EMAIL>");
        when(authService.createOAuth2User(any(Users.class))).thenReturn(user);
        // Use reflection to call private method
        Method method = CustomOAuth2UserService.class.getDeclaredMethod("processOAuth2User", OAuth2UserRequest.class, OAuth2User.class);
        method.setAccessible(true);
        Object result = method.invoke(service, userRequest, oAuth2User);
        assertNotNull(result);
        assertTrue(result instanceof UserPrincipal);
    }

    @Test
    void testProcessOAuth2User_updatesExistingUser() throws Exception {
        OAuth2UserRequest userRequest = mock(OAuth2UserRequest.class);
        ClientRegistration clientRegistration = mock(ClientRegistration.class);
        when(userRequest.getClientRegistration()).thenReturn(clientRegistration);
        when(clientRegistration.getRegistrationId()).thenReturn("google");
        Map<String, Object> attributes = new HashMap<>();
        attributes.put("email", "<EMAIL>");
        attributes.put("name", "Test User");
        attributes.put("sub", "123");
        attributes.put("picture", "http://img");
        OAuth2User oAuth2User = new DefaultOAuth2User(Collections.emptyList(), attributes, "sub");
        Users user = new Users();
        user.setEmail("<EMAIL>");
        user.setSocialLoginProvider("google");
        when(authService.findUserByEmail(anyString())).thenReturn(Optional.of(user));
        when(authService.updateOAuth2User(any(Users.class))).thenReturn(user);
        Method method = CustomOAuth2UserService.class.getDeclaredMethod("processOAuth2User", OAuth2UserRequest.class, OAuth2User.class);
        method.setAccessible(true);
        Object result = method.invoke(service, userRequest, oAuth2User);
        assertNotNull(result);
        assertTrue(result instanceof UserPrincipal);
    }

    @Test
    void testProcessOAuth2User_emailNotFound() throws Exception {
        OAuth2UserRequest userRequest = mock(OAuth2UserRequest.class);
        ClientRegistration clientRegistration = mock(ClientRegistration.class);
        when(userRequest.getClientRegistration()).thenReturn(clientRegistration);
        when(clientRegistration.getRegistrationId()).thenReturn("google");
        Map<String, Object> attributes = new HashMap<>();
        // Add some attributes but no email
        attributes.put("sub", "123");
        attributes.put("name", "Test User");
        OAuth2User oAuth2User = new DefaultOAuth2User(Collections.emptyList(), attributes, "sub");
        Method method = CustomOAuth2UserService.class.getDeclaredMethod("processOAuth2User", OAuth2UserRequest.class, OAuth2User.class);
        method.setAccessible(true);
        OAuth2AuthenticationProcessingException ex = assertThrows(OAuth2AuthenticationProcessingException.class, () -> {
            try {
                method.invoke(service, userRequest, oAuth2User);
            } catch (InvocationTargetException ite) {
                if (ite.getCause() instanceof OAuth2AuthenticationProcessingException) {
                    throw (OAuth2AuthenticationProcessingException) ite.getCause();
                }
                throw ite;
            }
        });
        assertTrue(ex.getMessage().contains("Email not found from OAuth2 provider"));
    }

    @Test
    void testProcessOAuth2User_providerMismatch() throws Exception {
        OAuth2UserRequest userRequest = mock(OAuth2UserRequest.class);
        ClientRegistration clientRegistration = mock(ClientRegistration.class);
        when(userRequest.getClientRegistration()).thenReturn(clientRegistration);
        when(clientRegistration.getRegistrationId()).thenReturn("google");
        Map<String, Object> attributes = new HashMap<>();
        attributes.put("email", "<EMAIL>");
        attributes.put("name", "Test User");
        attributes.put("sub", "123");
        attributes.put("picture", "http://img");
        OAuth2User oAuth2User = new DefaultOAuth2User(Collections.emptyList(), attributes, "sub");
        Users user = new Users();
        user.setEmail("<EMAIL>");
        user.setSocialLoginProvider("facebook"); // Different provider
        when(authService.findUserByEmail(anyString())).thenReturn(Optional.of(user));
        Method method = CustomOAuth2UserService.class.getDeclaredMethod("processOAuth2User", OAuth2UserRequest.class, OAuth2User.class);
        method.setAccessible(true);
        OAuth2AuthenticationProcessingException ex = assertThrows(OAuth2AuthenticationProcessingException.class, () -> {
            try {
                method.invoke(service, userRequest, oAuth2User);
            } catch (InvocationTargetException ite) {
                if (ite.getCause() instanceof OAuth2AuthenticationProcessingException) {
                    throw (OAuth2AuthenticationProcessingException) ite.getCause();
                }
                throw ite;
            }
        });
        assertTrue(ex.getMessage().contains("Looks like you're signed up with facebook account"));
    }

    // Helper subclass to override super.loadUser
    static class TestCustomOAuth2UserService extends CustomOAuth2UserService {
        private final OAuth2User userToReturn;
        public TestCustomOAuth2UserService(OAuth2User userToReturn) {
            this.userToReturn = userToReturn;
        }
        @Override
        public OAuth2User loadUser(OAuth2UserRequest oAuth2UserRequest) throws OAuth2AuthenticationException {
            // Override to skip the super.loadUser call and directly process our test user
            try {
                Method method = CustomOAuth2UserService.class.getDeclaredMethod("processOAuth2User", OAuth2UserRequest.class, OAuth2User.class);
                method.setAccessible(true);
                return (OAuth2User) method.invoke(this, oAuth2UserRequest, userToReturn);
            } catch (Exception e) {
                throw new InternalAuthenticationServiceException(e.getMessage(), e.getCause());
            }
        }
    }

    @Test
    void testLoadUser_success() throws Exception {
        OAuth2UserRequest userRequest = mock(OAuth2UserRequest.class);
        ClientRegistration clientRegistration = mock(ClientRegistration.class);
        when(userRequest.getClientRegistration()).thenReturn(clientRegistration);
        when(clientRegistration.getRegistrationId()).thenReturn("google");
        Map<String, Object> attributes = new HashMap<>();
        attributes.put("email", "<EMAIL>");
        attributes.put("name", "Test User");
        attributes.put("sub", "123");
        attributes.put("picture", "http://img");
        OAuth2User oAuth2User = new DefaultOAuth2User(Collections.emptyList(), attributes, "sub");
        when(authService.findUserByEmail(anyString())).thenReturn(Optional.empty());
        Users user = new Users();
        user.setEmail("<EMAIL>");
        when(authService.createOAuth2User(any(Users.class))).thenReturn(user);
        
        // Use the test subclass
        TestCustomOAuth2UserService testService = new TestCustomOAuth2UserService(oAuth2User);
        // Inject mocks
        var authServiceField = CustomOAuth2UserService.class.getDeclaredField("authService");
        authServiceField.setAccessible(true);
        authServiceField.set(testService, authService);
        var userVerificationDaoField = CustomOAuth2UserService.class.getDeclaredField("userVerificationDao");
        userVerificationDaoField.setAccessible(true);
        userVerificationDaoField.set(testService, userVerificationDao);
        var userSessionsDaoField = CustomOAuth2UserService.class.getDeclaredField("userSessionsDao");
        userSessionsDaoField.setAccessible(true);
        userSessionsDaoField.set(testService, userSessionsDao);
        doNothing().when(userSessionsDao).insert(any(com.chidhagni.auth.db.jooq.tables.pojos.UserSessions.class));
        
        OAuth2User result = testService.loadUser(userRequest);
        assertNotNull(result);
        assertTrue(result instanceof UserPrincipal);
    }

    @Test
    void testLoadUser_exceptionWrapped() throws Exception {
        OAuth2UserRequest userRequest = mock(OAuth2UserRequest.class);
        ClientRegistration clientRegistration = mock(ClientRegistration.class);
        when(userRequest.getClientRegistration()).thenReturn(clientRegistration);
        when(clientRegistration.getRegistrationId()).thenReturn("google");
        Map<String, Object> attributes = new HashMap<>();
        // Add some attributes but no email to cause exception
        attributes.put("sub", "123");
        attributes.put("name", "Test User");
        OAuth2User oAuth2User = new DefaultOAuth2User(Collections.emptyList(), attributes, "sub");
        
        // Use the test subclass
        TestCustomOAuth2UserService testService = new TestCustomOAuth2UserService(oAuth2User);
        // Inject mocks
        var authServiceField = CustomOAuth2UserService.class.getDeclaredField("authService");
        authServiceField.setAccessible(true);
        authServiceField.set(testService, authService);
        var userVerificationDaoField = CustomOAuth2UserService.class.getDeclaredField("userVerificationDao");
        userVerificationDaoField.setAccessible(true);
        userVerificationDaoField.set(testService, userVerificationDao);
        
        InternalAuthenticationServiceException ex = assertThrows(InternalAuthenticationServiceException.class, () -> {
            testService.loadUser(userRequest);
        });
        
        // Check either the message or the cause message
        String message = ex.getMessage();
        String causeMessage = ex.getCause() != null ? ex.getCause().getMessage() : null;
        assertTrue((message != null && message.contains("Email not found from OAuth2 provider")) ||
                   (causeMessage != null && causeMessage.contains("Email not found from OAuth2 provider")));
    }

    @Test
    void testUpdateExistingUser() throws Exception {
        Users existingUser = new Users();
        existingUser.setEmail("<EMAIL>");
        existingUser.setName("Old Name");
        existingUser.setSocialLoginProviderImageUrl("old-image.jpg");
        
        Map<String, Object> attributes = new HashMap<>();
        attributes.put("name", "New Name");
        attributes.put("picture", "new-image.jpg");
        
        // Create OAuth2UserInfo using the factory
        OAuth2UserInfo oAuth2UserInfo = OAuth2UserInfoFactory.getOAuth2UserInfo("google", attributes);
        
        when(authService.updateOAuth2User(any(Users.class))).thenReturn(existingUser);
        
        Method method = CustomOAuth2UserService.class.getDeclaredMethod("updateExistingUser", Users.class, OAuth2UserInfo.class);
        method.setAccessible(true);
        Users result = (Users) method.invoke(service, existingUser, oAuth2UserInfo);
        
        assertNotNull(result);
        verify(authService).updateOAuth2User(existingUser);
    }
} 