# SonarQube project configuration for NestJS/TypeScript application
sonar.projectKey=AI-Nest-Backend
sonar.projectName=AI Nest Backend
sonar.projectVersion=1.0

# Source and test directories
sonar.sources=src
sonar.tests=test
# For local testing only - remove before committing:
# sonar.host.url=http://***************:9000
# sonar.token=


# Language and file patterns for TypeScript/JavaScript
sonar.language=ts
sonar.sourceEncoding=UTF-8

# Include TypeScript and JavaScript files
sonar.inclusions=src/**/*.ts,src/**/*.js
sonar.test.inclusions=test/**/*.spec.ts,test/**/*.e2e-spec.ts,**/*.test.ts

# Exclusions
sonar.exclusions=**/node_modules/**,**/dist/**,**/coverage/**,**/*.d.ts
sonar.coverage.exclusions=src/database/**/*,**/node_modules/**,**/dist/**

# Coverage reports (both TypeScript and JavaScript)
sonar.typescript.lcov.reportPaths=coverage/lcov.info
sonar.javascript.lcov.reportPaths=coverage/lcov.info

# TypeScript specific settings
sonar.typescript.node=node

# Host URL and token will be provided via environment variables in CI/CD