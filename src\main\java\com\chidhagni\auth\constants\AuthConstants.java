package com.chidhagni.auth.constants;

public class AuthConstants {

    private AuthConstants() {
        // Private constructor to prevent instantiation
    }

    public static final int VERIFICATION_TOKEN_BYTES = 32; // 256 bits
    public static final int RESET_TOKEN_BYTES = 32; // 256 bits
    public static final int SESSION_TOKEN_BYTES = 64; // 512 bits
    public static final int REFRESH_TOKEN_BYTES = 64; // 512 bits

    public static final int VERIFICATION_TOKEN_EXPIRY_MINUTES = 30;
    public static final int RESET_TOKEN_EXPIRY_MINUTES = 30;
    public static final int SESSION_EXPIRY_HOURS = 2;
    public static final int REFRESH_TOKEN_EXPIRY_DAYS = 7;

    public static final int MAX_FAILED_LOGIN_ATTEMPTS = 5;

    public static final String VERIFICATION_LINK_TEMPLATE = "/verify-email?token=%s";
    public static final String RESET_LINK_TEMPLATE = "/reset-password?token=%s";

    // === Custom MIME Types ===
    public static final String REGISTER_REQUEST_MIME = "application/vnd-chidhagni-auth.register.create.req-v1+json";
    public static final String REGISTER_RESPONSE_MIME = "application/vnd-chidhagni-auth.register.create.res-v1+json";

    public static final String VERIFY_EMAIL_RESPONSE_MIME = "application/vnd-chidhagni-auth.verify-email.get.res-v1+json";

    public static final String CREATE_USER_REQUEST_MIME = "application/vnd-chidhagni-auth.create-user.create.req-v1+json";
    public static final String CREATE_USER_RESPONSE_MIME = "application/vnd-chidhagni-auth.create-user.create.res-v1+json";

    public static final String RESEND_VERIFICATION_LINK_REQUEST_MIME = "application/vnd-chidhagni-auth.resend-verification-link.create.req-v1+json";
    public static final String RESEND_VERIFICATION_LINK_RESPONSE_MIME = "application/vnd-chidhagni-auth.resend-verification-link.create.res-v1+json";

    public static final String FORGOT_PASSWORD_REQUEST_MIME = "application/vnd-chidhagni-auth.forgot-password.create.req-v1+json";
    public static final String FORGOT_PASSWORD_RESPONSE_MIME = "application/vnd-chidhagni-auth.forgot-password.create.res-v1+json";

    public static final String RESET_PASSWORD_REQUEST_MIME = "application/vnd-chidhagni-auth.reset-password.create.req-v1+json";
    public static final String RESET_PASSWORD_RESPONSE_MIME = "application/vnd-chidhagni-auth.reset-password.create.res-v1+json";

    public static final String LOGIN_REQUEST_MIME = "application/vnd-chidhagni-auth.login.create.req-v1+json";
    public static final String LOGIN_RESPONSE_MIME = "application/vnd-chidhagni-auth.login.create.res-v1+json";

    public static final String LOGOUT_REQUEST_MIME = "application/vnd-chidhagni-auth.logout.create.req-v1+json";
    public static final String LOGOUT_RESPONSE_MIME = "application/vnd-chidhagni-auth.logout.create.res-v1+json";

    public static final String GET_USER_RESPONSE_MIME = "application/vnd-chidhagni-auth.user.get.res-v1+json";
}