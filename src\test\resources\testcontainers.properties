# Testcontainers configuration for CI environment
ryuk.container.privileged=true
# Disable container reuse in CI to avoid lifecycle issues
testcontainers.reuse.enable=false
testcontainers.docker.client.strategy=org.testcontainers.dockerclient.UnixSocketClientProviderStrategy
ryuk.container.timeout=60
testcontainers.ryuk.disabled=true
# Additional CI-specific settings
testcontainers.docker.socket.override=/var/run/docker.sock
testcontainers.host.override=localhost