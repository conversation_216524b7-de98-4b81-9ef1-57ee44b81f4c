# Migration Setup for Docker & Production

This document explains how database migrations work in different environments and how to set them up properly.

## Overview

The application uses Knex.js for database migrations with different configurations for development, production, and Docker environments.

## File Structure

```
├── knexfile.js                    # Production knexfile (uses compiled config)
├── src/database/knexfile.ts       # TypeScript knexfile (development)
├── src/database/migrations/       # Migration files
└── dist/src/database/migrations/  # Compiled migrations (production)
```

## Environment-Specific Configuration

### Development Environment
- **File**: `src/database/knexfile.ts`
- **Migrations**: TypeScript (`.ts`)
- **Command**: `npm run migration:latest:dev`

### Production Environment
- **File**: `knexfile.js` (loads compiled config)
- **Migrations**: JavaScript (`.js`)
- **Command**: `npm run migration:latest`

### Docker Environment
- **File**: `knexfile.js` (copied to container)
- **Migrations**: Compiled JavaScript
- **Command**: `npm run migration:latest` (in entrypoint)

## How It Works

### 1. Development (Local)
```bash
# Uses TypeScript knexfile directly
npm run migration:latest:dev
```

### 2. Production (Docker)
```bash
# Build process compiles TypeScript to JavaScript
npm run build

# Docker copies knexfile.js and compiled migrations
# Entrypoint runs migrations before starting app
npm run migration:latest
```

### 3. CI/CD Pipeline
```bash
# Same as production - uses knexfile.js
npm run migration:latest
```

## Docker Build Process

1. **Build Stage**:
   - Compiles TypeScript to JavaScript
   - Compiles migrations to `dist/src/database/migrations/`
   - Creates `knexfile.js` that loads compiled config

2. **Production Stage**:
   - Copies compiled app and migrations
   - Copies `knexfile.js` to container root
   - Runs migrations via entrypoint script

## Testing the Setup

Run the migration setup test:

```bash
npm run test:migration-setup
```

This verifies:
- ✅ `knexfile.js` loads correctly
- ✅ All environments are configured
- ✅ Production uses JS migrations

## Troubleshooting

### Common Issues

1. **"Cannot find module './dist/src/database/knexfile'"**
   - Ensure `npm run build` completed successfully
   - Check that `dist/` directory exists

2. **"No knexfile found"**
   - Ensure `knexfile.js` exists in project root
   - Check Dockerfile copies the file correctly

3. **"Migrations not found"**
   - Verify migrations are compiled to `dist/src/database/migrations/`
   - Check Dockerfile compilation step

### Debugging Commands

```bash
# Test knexfile loading
node test-migration-setup.js

# Check compiled migrations
ls -la dist/src/database/migrations/

# Test migration command
npm run migration:latest

# Check Docker build
docker build -t test-migration .
```

## Migration Commands

| Environment | Command | Description |
|-------------|---------|-------------|
| Development | `npm run migration:latest:dev` | Run migrations with TypeScript |
| Production | `npm run migration:latest` | Run migrations with JavaScript |
| Docker | `npm run migration:latest` | Auto-run in entrypoint |

## Best Practices

1. **Always test migrations locally first**
2. **Use `migration:latest:dev` for development**
3. **Use `migration:latest` for production/Docker**
4. **Run `npm run test:migration-setup` before deploying**
5. **Keep migration files in version control**

## File Changes Summary

### Added Files
- `knexfile.js` - Production knexfile
- `test-migration-setup.js` - Setup verification script
- `docs/MIGRATION_SETUP.md` - This documentation

### Modified Files
- `src/database/knexfile.ts` - Added production/test configs
- `package.json` - Updated migration scripts
- `Dockerfile` - Added migration compilation
- `docker-entrypoint.sh` - Updated to start app after migrations

### Script Changes
- `migration:latest` - Now uses `knexfile.js`
- `migration:latest:dev` - Uses TypeScript knexfile
- `test:migration-setup` - Verifies setup 