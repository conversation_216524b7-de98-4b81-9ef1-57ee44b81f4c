package com.chidhagni.auth.controller;

import com.chidhagni.auth.dto.AdminUserListResponse;
import com.chidhagni.auth.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/admin")
@Tag(name = "Admin", description = "Admin-only endpoints")
@Slf4j
@RequiredArgsConstructor
public class AdminController {

    private final AuthService authService;

    @GetMapping("/users")
    @Operation(
        summary = "Get all users",
        description = "Retrieves all users in the system. This endpoint is restricted to admin users only."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Users retrieved successfully",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = AdminUserListResponse.class),
                examples = @ExampleObject(
                    value = "{\"users\": [{\"id\": \"123e4567-e89b-12d3-a456-************\", \"email\": \"<EMAIL>\", \"name\": \"John Doe\", \"mobileNumber\": \"**********\", \"isActive\": true, \"isEmailVerified\": true, \"isAccountLocked\": false, \"createdOn\": \"2024-01-01T12:00:00Z\", \"updatedOn\": \"2024-01-01T12:00:00Z\"}], \"totalCount\": 1, \"message\": \"Users retrieved successfully\"}"
                )
            )
        ),
        @ApiResponse(
            responseCode = "403",
            description = "Access denied - Admin role required",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    value = "{\"status\": 403, \"error\": \"Forbidden\", \"message\": \"Access denied\"}"
                )
            )
        ),
        @ApiResponse(
            responseCode = "401",
            description = "Unauthorized - Authentication required",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    value = "{\"status\": 401, \"error\": \"Unauthorized\", \"message\": \"Authentication required\"}"
                )
            )
        )
    })
    @PreAuthorize("hasRole('ADMIN')")
    public AdminUserListResponse getAllUsers() {
        log.info("Admin requesting all users");
        return authService.getAllUsers();
    }
} 